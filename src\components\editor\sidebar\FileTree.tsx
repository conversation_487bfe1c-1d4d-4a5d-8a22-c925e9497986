"use client";

import React from "react";
import FileTreeItem from "./FileTreeItem";
import ContextMenu from "./ContextMenu";
import type { FileTreeProps } from "./types";

const FileTree: React.FC<FileTreeProps> = ({
  tree,
  onToggle,
  onDrop,
  draggedItem,
  setDraggedItem,
  createPageLink,
  handlePageClick,
  getItemClasses,
  onCreateItem,
  onRenameItem,
  onDeleteItem,
  editingNodeId,
  setEditingNodeId,
}) => {
  // Nó virtual para representar a raiz
  const rootNode = {
    id: "root",
    name: "<PERSON><PERSON>",
    type: "navbar" as const,
    parentId: undefined,
    children: tree,
    isExpanded: true,
    data: { type: "root" },
  };

  if (!tree || tree.length === 0) {
    // Retorna um container vazio que ainda permite o menu de contexto
    // A mensagem de "Right-click to get started" foi removida para
    // evitar duplicação com o estado de carregamento/skeleton.
    return (
      <ContextMenu
        node={rootNode}
        onCreateItem={onCreateItem}
        onRenameItem={onRenameItem}
        onDeleteItem={onDeleteItem}
        editingNodeId={editingNodeId}
        setEditingNodeId={setEditingNodeId}
      >
        <div className="h-full w-full cursor-pointer" />
      </ContextMenu>
    );
  }

  return (
    <ContextMenu
      node={rootNode}
      onCreateItem={onCreateItem}
      onRenameItem={onRenameItem}
      onDeleteItem={onDeleteItem}
      editingNodeId={editingNodeId}
      setEditingNodeId={setEditingNodeId}
    >
      <div className="flex flex-col h-full w-full">
        {/* Container dos itens da árvore */}
        <div className="p-2 space-y-0.5">
          {tree.map((node) => (
            <FileTreeItem
              key={node.id}
              node={node}
              level={0}
              fullTree={tree}
              onToggle={onToggle}
              onDrop={onDrop}
              draggedItem={draggedItem}
              setDraggedItem={setDraggedItem}
              createPageLink={createPageLink}
              handlePageClick={handlePageClick}
              getItemClasses={getItemClasses}
              onCreateItem={onCreateItem}
              onRenameItem={onRenameItem}
              onDeleteItem={onDeleteItem}
              editingNodeId={editingNodeId}
              setEditingNodeId={setEditingNodeId}
            />
          ))}
        </div>

        {/* Área extra que ocupa todo o restante do espaço para context menu */}
        <div className="flex-1 cursor-pointer" />
      </div>
    </ContextMenu>
  );
};

export default FileTree;
