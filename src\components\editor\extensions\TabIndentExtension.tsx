import { Extension } from "@tiptap/core";

export const TabIndentExtension = Extension.create({
  name: "tabIndent",

  addKeyboardShortcuts() {
    return {
      Tab: ({ editor }) => {
        // Check if we're in a list
        if (editor.isActive("bulletList") || editor.isActive("orderedList")) {
          // Try to sink the list item (increase indentation)
          const result = editor.commands.sinkListItem("listItem");
          // If sinking succeeded, return true to prevent default behavior
          if (result) {
            return true;
          }
          // If sinking failed (already at max depth), still prevent default tab behavior
          return true;
        }

        // Check if we're in a table
        if (editor.isActive("table")) {
          return editor.commands.goToNextCell();
        }

        // For code blocks, insert actual tab character
        if (editor.isActive("codeBlock")) {
          return editor.commands.insertContent("\t");
        }

        // For regular text, insert 2 spaces for indentation
        // This provides consistent indentation behavior
        return editor.commands.insertContent("  ");
      },

      "Shift-Tab": ({ editor }) => {
        // Check if we're in a list
        if (editor.isActive("bulletList") || editor.isActive("orderedList")) {
          // Try to lift the list item (decrease indentation)
          const result = editor.commands.liftListItem("listItem");
          // If lifting succeeded, return true to prevent default behavior
          if (result) {
            return true;
          }
          // If lifting failed (already at root level), still prevent default behavior
          return true;
        }

        // Check if we're in a table
        if (editor.isActive("table")) {
          return editor.commands.goToPreviousCell();
        }

        // For regular text, try to remove indentation (2 spaces)
        const { state } = editor;
        const { selection } = state;
        const { $from } = selection;

        // Get the text before the cursor in the current line
        const textBefore = $from.parent.textBetween(0, $from.parentOffset);

        // If the line starts with spaces, remove up to 2 spaces
        if (textBefore.match(/^  /)) {
          const from = $from.pos - $from.parentOffset;
          const to = from + 2;
          return editor.commands.deleteRange({ from, to });
        }

        // If no indentation to remove, prevent default behavior
        return true;
      },
    };
  },
});

export default TabIndentExtension;
