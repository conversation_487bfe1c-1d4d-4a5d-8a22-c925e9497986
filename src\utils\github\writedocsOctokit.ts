import { Octokit, App } from "octokit";

const writedocsApp = new App({
  appId: process.env.WD_GITHUB_APP_ID as string,
  privateKey: process.env.WD_GITHUB_APP_PRIVATE_KEY as string,
});

async function getInstallationToken() {
  try {
    const installations = await writedocsApp.octokit.request(
      "GET /app/installations"
    );

    const installation = installations.data.find(
      (inst) =>
        inst.account?.login === (process.env.WD_GITHUB_ORG_NAME as string)
    );

    if (!installation) {
      throw new Error(
        `No installation found for organization: ${process.env.WD_GITHUB_ORG_NAME}`
      );
    }

    const response = await writedocsApp.octokit.request(
      `POST /app/installations/${installation.id}/access_tokens`
    );

    return response.data.token;
  } catch (error) {
    console.log(
      "Error getting installation token:",
      error instanceof Error && "response" in error
        ? error.response
        : "Unknown error"
    );
    throw error;
  }
}

async function createOctokitInstance() {
  const installationToken = await getInstallationToken();

  return new Octokit({
    auth: installationToken,
  });
}

export async function writedocsOctokit() {
  return await createOctokitInstance();
}
