// Mock projects configuration
// Add/remove project names here to enable/disable mock data

export const MOCK_PROJECTS = [
	"pagbank-docs", // Add more project names here as needed
];

/**
 * Check if a project should use mock data
 */
export const shouldUseMockData = (projectName: string): boolean => {
	return MOCK_PROJECTS.includes(projectName.toLowerCase());
};

/**
 * Get personalized project name for mocks
 */
export const getMockProjectDisplayName = (projectName: string): string => {
	// Capitalize first letter of each word for display
	return projectName
		.split("-")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(" ");
};
