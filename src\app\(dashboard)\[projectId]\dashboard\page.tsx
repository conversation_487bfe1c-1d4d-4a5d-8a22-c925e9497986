"use client";

import { useParams } from "next/navigation";
import { useProject } from "@/contexts";
import { useState, useEffect } from "react";
import {
  VisitsSummaryChart,
  VisitsSummaryChartSkeleton,
} from "@/components/ui/chart";
import {
  MetricsCards,
  ErrorCard,
  TopCountries,
  TopSources,
  TechStatsGrid,
  TopPages,
  // SiteInformation,
  // DebugSection,
  TimeRangeSelector,
  TimeRange,
  NoAnalyticsData,
} from "@/components/dashboard";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { DownloadIcon } from "lucide-react";
import { shouldUseMockData } from "@/utils/mocks/mockProjects";
import {
  generateMockAnalyticsData,
  generateMockTopNsData,
  generateMockSparklineData,
  generateMockTimeseriesData,
} from "@/utils/mocks/dashboardMockData";

/* interface CloudflareZone {
	id: string;
	name: string;
	status: string;
} */

/* interface ZoneListData {
	success: boolean;
	data: CloudflareZone[];
} */

interface AnalyticsData {
  dashboard?: {
    timeseries?: Array<unknown>;
    totals?: {
      requests?: number;
      bandwidth?: number;
      uniques?: number;
      pageViews?: number;
      threats?: number;
      bounceRate?: number;
      avgPageLoadTime?: number;
    };
  };
  firewall?: Array<unknown>;
  zone?: unknown;
  zoneId?: string;
  zoneName?: string;
  siteTag?: string;
  siteName?: string;
  projectName?: string;
  timeRange?: {
    since: string;
    until: string;
  };
  apiVersion?: string;
  debug?: {
    availableSites?: WebAnalyticsSite[];
    searchedKeywords?: string[];
    message?: string;
    suggestion?: string;
  };
}

/* interface CloudflareProject {
	type?: string;
	project?: unknown;
	success?: boolean;
	data?: unknown;
} */

/* interface FindProjectByRepoData {
	project?: unknown;
	error?: string;
} */

interface WebAnalyticsSite {
  site_tag: string;
  host: string;
}

interface WebAnalyticsTopNsResponse {
  success: boolean;
  siteTag: string;
  dateRange: { since: string; until: string };
  data: {
    countries: Array<{
      count: number;
      sum: { visits: number };
      dimensions: { metric: string };
    }>;
    topReferers: Array<{
      count: number;
      sum: { visits: number };
      dimensions: { metric: string };
    }>;
    topPaths: Array<{
      count: number;
      sum: { visits: number };
      dimensions: { metric: string };
    }>;
    topBrowsers: Array<{
      count: number;
      sum: { visits: number };
      dimensions: { metric: string };
    }>;
    topOSs: Array<{
      count: number;
      sum: { visits: number };
      dimensions: { metric: string };
    }>;
    topDeviceTypes: Array<{
      count: number;
      sum: { visits: number };
      dimensions: { metric: string };
    }>;
  };
}

interface WebAnalyticsSparklineResponse {
  success: boolean;
  siteTag: string;
  dateRange: { since: string; until: string };
  data: {
    visits?: Array<{
      sum: { visits: number };
      dimensions: { ts: string };
    }>;
    pageviews?: Array<{
      count: number;
      dimensions: { ts: string };
    }>;
  };
}

interface WebAnalyticsTimeseriesResponse {
  success: boolean;
  siteTag: string;
  dateRange: { since: string; until: string };
  groupBy?: string;
  data: {
    series?: Array<{
      count: number;
      sum: { visits: number };
      dimensions: { ts: string; metric: string };
    }>;
  };
}

export default function ProjectDashboardPage() {
  const { projectId } = useParams();
  const { projects, isLoadingProjects } = useProject();
  /* const [cloudflareProject, setCloudflareProject] =
		useState<CloudflareProject | null>(null); */
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(
    null
  );
  const [topnsData, setTopnsData] = useState<WebAnalyticsTopNsResponse | null>(
    null
  );
  const [sparklineData, setSparklineData] =
    useState<WebAnalyticsSparklineResponse | null>(null);
  const [timeseriesData, setTimeseriesData] = useState<{
    [key: string]: WebAnalyticsTimeseriesResponse;
  }>({});
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false);
  const [noMatchFound, setNoMatchFound] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // DEFAULT TIME RANGE CONFIGURATION
  // Fixed label from "Previous 7 days" to "Last 7 days" for consistency with preset ranges
  // This matches the preset label format and prevents confusion in the UI
  const [currentTimeRange, setCurrentTimeRange] = useState<TimeRange>({
    label: "Last 7 days",
    value: "7d",
    since: "-7d",
    until: "now",
  });
  const [isExporting, setIsExporting] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const project = projects.find((p) => p.id.toString() === projectId);

  /* const handleFindCloudflareProject = async () => {
		// @ts-expect-error -- We are assuming github_repository_url exists on the project object
		if (!project || !project.github_repository_url) {
			console.error(
				"Project or project.github_repository_url is not available."
			);
			setError("Project repository URL not found.");
			return;
		}

		try {
			// @ts-expect-error -- We are assuming github_repository_url exists on the project object
			const repoUrl = new URL(project.github_repository_url);
			const repoName = repoUrl.pathname.split("/").pop();

			if (!repoName) {
				setError("Could not extract repository name from URL.");
				return;
			}
			const response = await fetch(
				`/api/cloudflare/project-by-repo?repoName=${repoName}`
			);
			const data: FindProjectByRepoData = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Failed to fetch project by repo.");
			}

			setCloudflareProject({ project: data.project });
		} catch (error) {
			console.error("Error fetching project by repo:", error);
			setError(
				error instanceof Error
					? error.message
					: "Error fetching project by repo"
			);
		}
	}; */

  const handleFetchAnalytics = async (timeRange?: TimeRange) => {
    const selectedRange = timeRange || currentTimeRange;
    setCurrentTimeRange(selectedRange);
    setIsLoadingAnalytics(true);
    setError(null);
    setNoMatchFound(false); // Reset on new fetch

    try {
      // Use the actual project name from the context
      if (!project?.project_name) {
        console.error(
          "Project name could not be determined. Aborting analytics fetch."
        );
        setError("Project not found, cannot fetch analytics.");
        setIsLoadingAnalytics(false);
        return;
      }
      const projectName = project.project_name;

      // Check if project should use mock data
      if (shouldUseMockData(projectName)) {
        // Generate mock data
        const mockAnalyticsData = generateMockAnalyticsData(
          projectName,
          selectedRange
        );
        const mockTopNsData = generateMockTopNsData(projectName, selectedRange);
        const mockSparklineData = generateMockSparklineData(
          projectName,
          selectedRange
        );

        // Generate timeseries data for all dimensions
        const dimensions = [
          "refererHost",
          "countryName",
          "requestPath",
          "userAgentBrowser",
          "userAgentOS",
          "deviceType",
        ];

        const mockTimeseriesData: {
          [key: string]: WebAnalyticsTimeseriesResponse;
        } = {};
        dimensions.forEach((dimension) => {
          mockTimeseriesData[dimension] = generateMockTimeseriesData(
            projectName,
            selectedRange,
            dimension
          );
        });

        // Set all mock data
        setAnalyticsData(mockAnalyticsData);
        setTopnsData(mockTopNsData);
        setSparklineData(mockSparklineData);
        setTimeseriesData(mockTimeseriesData);
        setIsLoadingAnalytics(false);
        setIsInitialLoad(false);
        return;
      }

      // Check if Web Analytics is available from server-side data
      if (!project.hasWebAnalytics) {
        // No Web Analytics available - show NoAnalyticsData component
        setNoMatchFound(true);
        setAnalyticsData({
          projectName: projectName,
          siteName: project.siteName, // Show project site name if available
          apiVersion: "No Web Analytics",
          debug: {
            message: `Web Analytics not available for project "${projectName}"`,
            suggestion:
              "Web Analytics needs to be configured in Cloudflare for this project",
          },
        });
        setIsLoadingAnalytics(false);
        return;
      }

      // Get siteTag and siteName from context (set by server-side layout)
      const siteTag = project.siteTag;
      const siteName = project.siteName;

      if (!siteTag) {
        // This shouldn't happen if hasWebAnalytics is true, but fallback
        setNoMatchFound(true);
        setAnalyticsData({
          projectName: projectName,
          apiVersion: "Web Analytics Configuration Error",
          debug: {
            message: `Web Analytics configuration error for project "${projectName}"`,
            suggestion: "Please contact support if this issue persists",
          },
        });
        setIsLoadingAnalytics(false);
        return;
      }

      const matchingSite = {
        site_tag: siteTag,
        host: siteName!,
      };

      // Define all dimensions for timeseries data
      const dimensions = [
        "refererHost",
        "countryName",
        "requestPath",
        "userAgentBrowser",
        "userAgentOS",
        "deviceType",
      ];

      // Now fetch all analytics data in a single parallel batch
      const allFetchPromises = [
        // 1. TopNs data
        fetch(
          `/api/cloudflare/web-analytics-sites?action=topns&siteTag=${matchingSite.site_tag}&since=${selectedRange.since}&until=${selectedRange.until}`
        ),
        // 2. Sparkline data
        fetch(
          `/api/cloudflare/web-analytics-sites?action=sparkline&siteTag=${matchingSite.site_tag}&since=${selectedRange.since}&until=${selectedRange.until}`
        ),
        // 3. Timeseries data for all dimensions
        ...dimensions.map((dimension) =>
          fetch(
            `/api/cloudflare/web-analytics-sites?action=timeseries&siteTag=${matchingSite.site_tag}&since=${selectedRange.since}&until=${selectedRange.until}&groupBy=${dimension}`
          )
        ),
      ];

      const allResponses = await Promise.all(allFetchPromises);

      // Process all responses in parallel
      const allJsonData = await Promise.all(
        allResponses.map((res) => (res.ok ? res.json() : null))
      );

      const topnsData = allJsonData[0] as WebAnalyticsTopNsResponse | null;
      const sparklineData =
        allJsonData[1] as WebAnalyticsSparklineResponse | null;
      const timeseriesResults = allJsonData.slice(
        2
      ) as (WebAnalyticsTimeseriesResponse | null)[];

      // Build timeseries data map
      const timeseriesMap: { [key: string]: WebAnalyticsTimeseriesResponse } =
        {};
      dimensions.forEach((dimension, index) => {
        const result = timeseriesResults[index];
        if (result && result.success) {
          timeseriesMap[dimension] = result;
        }
      });

      // Manually calculate metrics from sparkline data
      let totalVisits = 0;
      let totalPageViews = 0;
      if (sparklineData?.data.visits) {
        totalVisits = sparklineData.data.visits.reduce(
          (sum, item) => sum + (item.sum?.visits || 0),
          0
        );
      }
      if (sparklineData?.data.pageviews) {
        totalPageViews = sparklineData.data.pageviews.reduce(
          (sum, item) => sum + (item.count || 0),
          0
        );
      }

      // Transform the real data to match our existing interface
      const transformedData: AnalyticsData = {
        dashboard: {
          totals: {
            requests: totalVisits,
            pageViews: totalPageViews,
            uniques: totalVisits, // Approximation
            bounceRate: 0, // Not available
            avgPageLoadTime: 0, // Not available
            bandwidth: 0, // Not available in Web Analytics
            threats: 0, // Not available in Web Analytics
          },
        },
        siteTag: matchingSite.site_tag,
        siteName: matchingSite.host,
        projectName: projectName,
        timeRange: {
          since: selectedRange.since,
          until: selectedRange.until,
        },
        apiVersion: "Web Analytics GraphQL API",
        debug: {
          message: "Real Web Analytics data fetched successfully! 🎉",
          suggestion: `Data showing ${selectedRange.label.toLowerCase()} from Cloudflare Web Analytics`,
          availableSites: [matchingSite],
        },
      };

      setAnalyticsData(transformedData);

      if (topnsData && topnsData.success) {
        setTopnsData(topnsData);
      }

      if (sparklineData && sparklineData.success) {
        setSparklineData(sparklineData);
      }

      setTimeseriesData(timeseriesMap);
    } catch (error) {
      console.error("Error fetching analytics:", error);
      setError(
        error instanceof Error ? error.message : "Unknown error occurred"
      );
    } finally {
      setIsLoadingAnalytics(false);
      setIsInitialLoad(false);
    }
  };

  /* const handleListZones = async () => {
		try {
			const response = await fetch("/api/cloudflare/web-analytics-sites");
			const data = (await response.json()) as ZoneListData;
			setCloudflareProject({
				type: "sites_list",
				success: data.success,
				data: data.data,
			});
		} catch (error) {
			console.error("Error fetching sites:", error);
			setError("Error fetching available sites");
		}
	}; */

  const exportToXLSX = async () => {
    if (!analyticsData?.projectName || !topnsData || isExporting) {
      return;
    }

    try {
      setIsExporting(true);

      let totalVisits = 0;
      let totalPageViews = 0;

      if (sparklineData?.data.visits) {
        totalVisits = sparklineData.data.visits.reduce(
          (sum, item) => sum + (item.sum?.visits || 0),
          0
        );
      }

      if (sparklineData?.data.pageviews) {
        totalPageViews = sparklineData.data.pageviews.reduce(
          (sum, item) => sum + (item.count || 0),
          0
        );
      }

      const exportData = {
        countries: topnsData.data.countries,
        topReferers: topnsData.data.topReferers,
        topPaths: topnsData.data.topPaths,
        topBrowsers: topnsData.data.topBrowsers,
        topOSs: topnsData.data.topOSs,
        topDeviceTypes: topnsData.data.topDeviceTypes,
      };

      const requestBody = {
        projectName: analyticsData.projectName,
        timeRangeLabel: currentTimeRange.label,
        totalVisits,
        totalPageViews,
        webAnalyticsData: exportData,
        dateRange: {
          since: currentTimeRange.since,
          until: currentTimeRange.until,
        },
      };

      const response = await fetch("/api/cloudflare/export-dashboard", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const contentDisposition = response.headers.get("Content-Disposition");
      const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
      const filename = filenameMatch
        ? filenameMatch[1]
        : `${analyticsData.projectName}_dashboard_export.xlsx`;

      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error("Error exporting XLSX:", err);
      setError("Failed to export data");
    } finally {
      setTimeout(() => {
        setIsExporting(false);
      }, 2000);
    }
  };

  // Auto-fetch analytics when component mounts and project info is available
  useEffect(() => {
    if (project && !analyticsData && !isLoadingAnalytics) {
      const isProjectMocked = shouldUseMockData(project.project_name);

      // Check if we have Web Analytics, or if it's a mock project
      if (
        (project.hasWebAnalytics === true && project.siteTag) ||
        isProjectMocked
      ) {
        // Has Web Analytics and siteTag available, or is a mock project - fetch analytics
        handleFetchAnalytics();
      } else if (project.hasWebAnalytics === false && !isProjectMocked) {
        // Definitely no Web Analytics and not a mock project - show NoAnalyticsData
        setNoMatchFound(true);
        setAnalyticsData({
          projectName: project.project_name,
          siteName: project.siteName,
          apiVersion: "No Web Analytics",
          debug: {
            message: `Web Analytics not available for project "${project.project_name}"`,
            suggestion:
              "Web Analytics needs to be configured in Cloudflare for this project",
          },
        });
      }
      // If hasWebAnalytics is undefined, we're still waiting for server-side data
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [project?.hasWebAnalytics, project?.siteTag, project?.id]); // React when Web Analytics info is loaded

  if (isLoadingProjects) {
    return (
      <div className="w-full pt-6 md:pt-10 px-4 md:px-6 overflow-x-hidden max-w-7xl mx-auto">
        {/* Header skeleton */}
        <div className="mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-end gap-4">
          <div className="flex flex-col gap-2">
            <Skeleton className="h-8 w-64" />
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
            <Skeleton className="h-10 w-40" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>

        {/* Metrics cards skeleton */}
        <div className="space-y-8">
          <MetricsCards
            analyticsData={{}}
            isLoading={true}
            timeRangeLabel="Loading..."
          />
          <VisitsSummaryChartSkeleton />
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Skeleton className="h-96" />
              <Skeleton className="h-96" />
            </div>
            <TechStatsGrid
              browsers={[]}
              operatingSystems={[]}
              deviceTypes={[]}
              isLoading={true}
            />
            <Skeleton className="h-96" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full pt-6 md:pt-10 px-4 md:px-6 max-w-7xl mx-auto overflow-x-hidden">
      <div className="mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-end gap-4">
        {/* header */}
        <div className="flex flex-col gap-2">
          <h1 className="text-2xl sm:text-3xl font-semibold break-words">
            {project?.project_name}
          </h1>
        </div>
        {/* time range selector and export button */}
        {!noMatchFound && (
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
            <TimeRangeSelector
              isLoading={isLoadingAnalytics}
              onRefresh={handleFetchAnalytics}
              selectedTimeRange={currentTimeRange}
            />
            <Button
              variant="outline"
              size="default"
              onClick={exportToXLSX}
              disabled={
                isLoadingAnalytics ||
                !topnsData ||
                !analyticsData?.projectName ||
                isExporting
              }
              className="w-full sm:w-auto"
            >
              <DownloadIcon className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">
                {isExporting ? "Exporting..." : "Export XLSX"}
              </span>
              <span className="sm:hidden">
                {isExporting ? "Exporting..." : "Export"}
              </span>
            </Button>
          </div>
        )}
      </div>

      {/* Web Analytics Section */}
      <div className="space-y-8">
        {error && <ErrorCard error={error} />}

        {noMatchFound ? (
          <NoAnalyticsData />
        ) : (
          <>
            <MetricsCards
              analyticsData={analyticsData || {}}
              isLoading={
                isLoadingAnalytics || (isInitialLoad && !analyticsData)
              }
              timeRangeLabel={currentTimeRange.label}
            />

            {/* Visits Summary Chart - Pass all data to avoid new requests */}
            {isLoadingAnalytics || sparklineData || isInitialLoad ? (
              <div>
                {isLoadingAnalytics || !sparklineData ? (
                  <VisitsSummaryChartSkeleton />
                ) : (
                  <VisitsSummaryChart
                    sparklineData={sparklineData}
                    timeseriesData={timeseriesData}
                  />
                )}
              </div>
            ) : null}

            {/* TopNs Section - Countries and Sources */}
            {(topnsData || isLoadingAnalytics || isInitialLoad) && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <TopCountries
                    countries={topnsData?.data.countries || []}
                    isLoading={
                      isLoadingAnalytics || (isInitialLoad && !topnsData)
                    }
                  />
                  <TopSources
                    sources={topnsData?.data.topReferers || []}
                    isLoading={
                      isLoadingAnalytics || (isInitialLoad && !topnsData)
                    }
                  />
                </div>

                {/* Tech Stats Grid */}
                <TechStatsGrid
                  browsers={topnsData?.data.topBrowsers || []}
                  operatingSystems={topnsData?.data.topOSs || []}
                  deviceTypes={topnsData?.data.topDeviceTypes || []}
                  isLoading={
                    isLoadingAnalytics || (isInitialLoad && !topnsData)
                  }
                />

                {/* Top Paths Section */}
                <TopPages
                  pages={topnsData?.data.topPaths || []}
                  isLoading={
                    isLoadingAnalytics || (isInitialLoad && !topnsData)
                  }
                />
              </div>
            )}
          </>
        )}
      </div>
      {/* Web Analytics Information */}
      {/* {analyticsData && !noMatchFound && (
				<div className='space-y-6 mt-6'>
					<SiteInformation
						siteName={analyticsData.siteName}
						projectName={analyticsData.projectName}
						siteTag={analyticsData.siteTag}
						apiVersion={analyticsData.apiVersion}
					/>
				</div>
			)} */}

      {/* Debug Section */}
      {/* <DebugSection
				cloudflareProject={cloudflareProject}
				onFindProject={handleFindCloudflareProject}
				onListSites={handleListZones}
			/> */}
    </div>
  );
}
