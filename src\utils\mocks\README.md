# Mock System

This system allows creating mock (simulated) data for specific projects, useful for demonstrations and client presentations.

## How to use

### Add a project to mocks

1. Edit the `mockProjects.ts` file
2. Add the project name to the `MOCK_PROJECTS` array:

```typescript
export const MOCK_PROJECTS = [
	"pagbank-docs", // Existing project
	"new-project", // Add here
];
```

### Remove a project from mocks

1. Edit the `mockProjects.ts` file
2. Remove the project name from the `MOCK_PROJECTS` array

### Mock project characteristics

- **Ask AI**: Don't need DocsBot keys configured
- **Dashboard**: Don't need to exist in Cloudflare
- **Personalized data**: Mocks use the project name to personalize data
- **Realistic data**: Generate statistics and logs that look real

### File structure

- `mockProjects.ts` - Configuration of which projects use mocks
- `askAiMockData.ts` - Mock data for Ask AI (statistics and logs)
- `dashboardMockData.ts` - Mock data for Dashboard (analytics)
- `index.ts` - Centralized exports

### Usage example

For the "pagbank-docs" project, mocks generate:

#### Ask AI:
- Question statistics, positive/negative responses
- Question logs with personalized payment responses
- Functional pagination
- Functional filters

#### Dashboard:
- Visit and pageview metrics
- Country data (Brazil, USA, Argentina, etc.)
- Browsers and operating systems
- Most visited pages (/api-reference, /getting-started, etc.)

### Important ⚠️

This system is for demonstrations. The commit with mocks will be deleted in the future, so use only for temporary presentations. 