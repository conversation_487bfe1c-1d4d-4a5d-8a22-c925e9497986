// Define your Project type here. This is a basic example.
// You might want to keep importing it from "next/dist/build/swc/types"
// or define a more specific interface based on your Supabase table structure.

import { ConfigJson } from "@/types/sidebarConfig";

// Interface for the raw data from the 'projects' table
export interface ProjectRecord {
  id: number;
  project_name: string;
  owner_id: string;
  website_url: string;
  deploy_repo_url: string;
  source_repo_url: string;
  company_name: string | null;
  auto_config: boolean;
  docusaurus_configs: unknown | null;
  // Add other fields from 'projects' table if they exist and are used in payload.new/old
}

// Interface for the raw data from the 'project_user' table
export interface ProjectUserRecord {
  user_id: string;
  project_id: number; // This is the foreign key to projects.id
  role: string;
  is_creator: boolean;
  // Add other fields from 'project_user' table if they exist and are used
}

export interface Project {
  // Fields from the 'projects' table
  id: number;
  project_name: string;
  owner_id: string;
  website_url: string;
  deploy_repo_url: string;
  source_repo_url: string;
  company_name: string | null;
  auto_config: boolean;
  docusaurus_configs: unknown | null;
  plan_json: {
    docsbot: string;
  };
  configjson: ConfigJson | null;

  // Fields from the 'project_user' table
  user_id: string;
  project_id: number;
  role: string;
  is_creator: boolean;

  // Cloudflare Web Analytics data - sempre presentes mas podem ser undefined
  siteTag: string | undefined;
  siteName: string | undefined; // hostname from Cloudflare
  hasWebAnalytics: boolean | undefined; // indica se o projeto tem Web Analytics habilitado
}

// Interface for the raw data from the 'project_invites' table
export interface ProjectInviteRecord {
  id: number;
  project_id: number;
  email: string;
  status: string;
  created_at: string;
}

export interface ProjectInvite {
  id: number;
  project_id: number;
  email: string;
  status: string;
  created_at: string;
  project: {
    id: number;
    project_name: string;
  };
}

export interface ProjectContextType {
  projects: Project[];
  setProjects: (projects: Project[]) => void;
  selectedProject: Project | null;
  setSelectedProject: (project: Project | null) => void;
  isLoadingProjects?: boolean;
  // Invite-related functionality
  invites: ProjectInvite[];
  hasInvites: boolean;
  inviteCount: number;
  isLoadingInvites: boolean;
  acceptInvite: (inviteId: number, projectId: number) => Promise<boolean>;
  declineInvite: (inviteId: number) => Promise<boolean>;
  refreshInvites: () => Promise<void>;
  // Project Info functionality (server-side)
  updateProjectInfo: (
    projectId: number,
    projectInfo: {
      siteName: string | undefined;
      siteTag: string | undefined;
      hasWebAnalytics: boolean | undefined;
    }
  ) => void;
}
