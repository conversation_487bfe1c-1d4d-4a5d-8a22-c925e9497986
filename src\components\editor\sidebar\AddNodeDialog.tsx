"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus } from "lucide-react";

interface AddNodeDialogProps {
  onAddNode: (nodeData: {
    type: "category" | "page" | "group";
    name: string;
    path?: string;
  }) => void;
}

export const AddNodeDialog: React.FC<AddNodeDialogProps> = ({ onAddNode }) => {
  const [open, setOpen] = useState(false);
  const [nodeType, setNodeType] = useState<"category" | "page" | "group">(
    "page"
  );
  const [name, setName] = useState("");
  const [path, setPath] = useState("");

  const handleSubmit = () => {
    if (!name.trim()) return;

    onAddNode({
      type: nodeType,
      name: name.trim(),
      path:
        nodeType === "page"
          ? path.trim() || name.toLowerCase().replace(/\s+/g, "-")
          : undefined,
    });

    // Reset form
    setName("");
    setPath("");
    setNodeType("page");
    setOpen(false);
  };

  const handleCancel = () => {
    setName("");
    setPath("");
    setNodeType("page");
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline" className="h-8 w-8 p-0">
          <Plus className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Adicionar Novo Item</DialogTitle>
          <DialogDescription>
            Crie uma nova página, categoria ou grupo de páginas.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="type" className="text-right">
              Tipo
            </Label>
            <Select
              value={nodeType}
              onValueChange={(value: "category" | "page" | "group") =>
                setNodeType(value)
              }
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="page">Página</SelectItem>
                <SelectItem value="category">Categoria</SelectItem>
                <SelectItem value="group">Grupo de Páginas</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Nome
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="col-span-3"
              placeholder="Nome do item"
            />
          </div>
          {nodeType === "page" && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="path" className="text-right">
                Caminho
              </Label>
              <Input
                id="path"
                value={path}
                onChange={(e) => setPath(e.target.value)}
                className="col-span-3"
                placeholder="docs/minha-pagina (opcional)"
              />
            </div>
          )}
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancelar
          </Button>
          <Button type="button" onClick={handleSubmit} disabled={!name.trim()}>
            Adicionar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
