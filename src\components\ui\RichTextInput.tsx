import React, { useEffect, useCallback, forwardRef, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Link from "@tiptap/extension-link";
import { cn } from "@/lib/utils";
import StepsNode from "../editor/extensions/StepsNode";
import {
  Bold,
  Italic,
  Strikethrough,
  Code,
  List,
  ListOrdered,
  Link as LinkIcon,
  Terminal,
  ListChecks,
} from "lucide-react";
import { LinkDialog } from "@/components/editor/components/LinkDialog";
import CodeBlockNode from "@/components/editor/extensions/CodeBlockNode";
import "./RichTextInput.css";

interface RichTextInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  className?: string;
  disabled?: boolean;
  variant?: "default" | "compact" | "minimal";
  showToolbar?: boolean;
  showCounter?: boolean;
  autoFocus?: boolean;
  id?: string;
  name?: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  // New props for enhanced features
  enableCodeBlock?: boolean;
  enableLink?: boolean;
  enableSteps?: boolean;
}

export const RichTextInput = forwardRef<HTMLDivElement, RichTextInputProps>(
  (
    {
      value,
      onChange,
      placeholder = "Digite aqui...",
      maxLength,
      className,
      disabled = false,
      variant = "default",
      showToolbar = true,
      showCounter = true,
      autoFocus = false,
      id,
      name,
      required = false,
      error = false,
      helperText,
      enableCodeBlock = false,
      enableLink = variant === "default",
      enableSteps = true,
      ...props
    },
    ref
  ) => {
    const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
    const [linkDialogData, setLinkDialogData] = useState<{
      selectedText: string;
      url?: string;
    }>({ selectedText: "" });

    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          // Disable features based on variant
          heading: variant === "minimal" ? false : { levels: [1, 2, 3] },
          codeBlock: false, // Disable default code block to use custom CodeBlockNode
          blockquote: variant === "minimal" ? false : {},
          horizontalRule: variant === "minimal" ? false : {},
          bulletList: variant === "minimal" ? false : {},
          orderedList: variant === "minimal" ? false : {},
          listItem: variant === "minimal" ? false : {},
          // Keep basic formatting
          bold: {},
          italic: {},
          strike: variant === "minimal" ? false : {},
          code: {},
          // Configure paragraph behavior
          paragraph: {
            HTMLAttributes: {
              class: "rich-text-paragraph",
            },
          },
        }),
        Placeholder.configure({
          placeholder: placeholder || "",
        }),
        // Add custom CodeBlockNode if enabled (same as TiptapEditor)
        ...(enableCodeBlock && variant !== "minimal" ? [CodeBlockNode] : []),
        // Add StepsNode if enabled
        ...(enableSteps && variant !== "minimal" ? [StepsNode] : []),
        // Add Link extension if enabled
        ...(enableLink
          ? [
              Link.configure({
                openOnClick: false,
                HTMLAttributes: {
                  class: "rich-text-link",
                },
              }),
            ]
          : []),
      ],
      content: value || "",
      editable: !disabled,
      editorProps: {
        attributes: {
          class: cn(
            "rich-text-editor",
            variant === "compact" && "rich-text-editor--compact",
            variant === "minimal" && "rich-text-editor--minimal",
            error && "rich-text-editor--error",
            className
          ),
          ...(id && { id }),
          ...(name && { "data-name": name }),
          "aria-required": required ? "true" : "false",
          "aria-invalid": error ? "true" : "false",
          ...(helperText && id && { "aria-describedby": `${id}-helper` }),
        },
        handleDOMEvents: {
          keydown: (view, event) => {
            // Prevent Enter in minimal mode (keep single line)
            if (
              variant === "minimal" &&
              event.key === "Enter" &&
              !event.shiftKey
            ) {
              event.preventDefault();
              return true;
            }

            return false;
          },
        },
      },
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        onChange(html);
      },
      onCreate: ({ editor }) => {
        if (autoFocus) {
          setTimeout(() => {
            editor.commands.focus();
          }, 0);
        }
      },
    });

    useEffect(() => {
      if (!editor) return;

      // Update content if it changes externally
      const currentContent = editor.getHTML();
      if (currentContent !== value && value !== editor.getHTML()) {
        editor.commands.setContent(value, false);
      }
    }, [value, editor]);

    useEffect(() => {
      if (!editor) return;

      // Update editable state
      editor.setEditable(!disabled);
    }, [disabled, editor]);

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        // Prevent event bubbling for certain keys to avoid form submission
        if (variant === "minimal" && e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          e.stopPropagation();
        }
      },
      [variant]
    );

    const handleLinkClick = useCallback(() => {
      if (!editor) return;

      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);

      // Check if we're in a link already
      const linkMark = editor.getAttributes("link");
      const currentUrl = linkMark.href || "";

      setLinkDialogData({
        selectedText:
          selectedText ||
          (currentUrl ? editor.state.doc.textBetween(from, to) : ""),
        url: currentUrl,
      });
      setIsLinkDialogOpen(true);
    }, [editor]);

    const handleLinkConfirm = useCallback(
      (url: string, text: string) => {
        if (!editor) return;

        const { from, to } = editor.state.selection;

        if (from === to) {
          // No selection, insert new link
          editor
            .chain()
            .focus()
            .insertContent(`<a href="${url}">${text}</a>`)
            .run();
        } else {
          // Has selection, apply link to selection or replace text
          editor
            .chain()
            .focus()
            .extendMarkRange("link")
            .setLink({ href: url })
            .insertContent(text)
            .run();
        }
      },
      [editor]
    );

    const insertCodeBlock = useCallback(() => {
      if (!editor) return;
      editor
        .chain()
        .focus()
        .insertContent({
          type: "codeBlock",
          attrs: {
            language: "javascript",
            code: "",
          },
        })
        .run();
    }, [editor]);

    const insertSteps = useCallback(() => {
      if (!editor) return;
      const now = Date.now();
      editor
        .chain()
        .focus()
        .insertContent({
          type: "steps",
          attrs: {
            steps: [
              {
                id: `${now}-1`,
                title: "Primeiro passo",
                content: "<p>Conteúdo do primeiro passo</p>",
                titleSize: "h3",
                subSteps: [],
              },
              {
                id: `${now}-2`,
                title: "Segundo passo",
                content: "<p>Conteúdo do segundo passo</p>",
                titleSize: "h3",
                subSteps: [],
              },
            ],
          },
        })
        .run();
    }, [editor]);

    if (!editor) {
      return (
        <div
          ref={ref}
          className={cn(
            "rich-text-input-placeholder",
            variant === "compact" && "rich-text-input-placeholder--compact",
            variant === "minimal" && "rich-text-input-placeholder--minimal",
            className
          )}
        >
          <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-6"></div>
        </div>
      );
    }

    const textLength = editor.state.doc.textContent.length;
    const isOverLimit = maxLength && textLength > maxLength;
    const isNearLimit = maxLength && textLength > maxLength * 0.9;

    return (
      <>
        <div
          ref={ref}
          className={cn(
            "rich-text-input-container",
            variant === "compact" && "rich-text-input-container--compact",
            variant === "minimal" && "rich-text-input-container--minimal"
          )}
          onKeyDown={handleKeyDown}
          {...props}
        >
          <EditorContent editor={editor} className="rich-text-input-content" />

          {/* Character Counter */}
          {maxLength && showCounter && (
            <div className="rich-text-input-counter">
              <span
                className={cn(
                  "text-xs",
                  isOverLimit
                    ? "text-red-500 dark:text-red-400"
                    : isNearLimit
                    ? "text-orange-500 dark:text-orange-400"
                    : "text-gray-500 dark:text-gray-400"
                )}
              >
                {textLength}/{maxLength}
              </span>
            </div>
          )}

          {/* Enhanced Floating Toolbar */}
          {showToolbar && variant !== "minimal" && (
            <div className="rich-text-input-toolbar">
              <button
                type="button"
                onClick={() => editor.chain().focus().toggleBold().run()}
                className={cn(
                  "toolbar-button",
                  editor.isActive("bold") && "toolbar-button--active"
                )}
                title="Negrito (Ctrl+B)"
                disabled={disabled}
              >
                <Bold className="w-4 h-4" />
              </button>
              <button
                type="button"
                onClick={() => editor.chain().focus().toggleItalic().run()}
                className={cn(
                  "toolbar-button",
                  editor.isActive("italic") && "toolbar-button--active"
                )}
                title="Itálico (Ctrl+I)"
                disabled={disabled}
              >
                <Italic className="w-4 h-4" />
              </button>
              {variant !== "compact" && (
                <>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleStrike().run()}
                    className={cn(
                      "toolbar-button",
                      editor.isActive("strike") && "toolbar-button--active"
                    )}
                    title="Riscado"
                    disabled={disabled}
                  >
                    <Strikethrough className="w-4 h-4" />
                  </button>
                  <button
                    type="button"
                    onClick={() => editor.chain().focus().toggleCode().run()}
                    className={cn(
                      "toolbar-button",
                      editor.isActive("code") && "toolbar-button--active"
                    )}
                    title="Código"
                    disabled={disabled}
                  >
                    <Code className="w-4 h-4" />
                  </button>

                  {/* Link button */}
                  {enableLink && (
                    <button
                      type="button"
                      onClick={handleLinkClick}
                      className={cn(
                        "toolbar-button",
                        editor.isActive("link") && "toolbar-button--active"
                      )}
                      title="Link"
                      disabled={disabled}
                    >
                      <LinkIcon className="w-4 h-4" />
                    </button>
                  )}

                  {/* Code Block button */}
                  {enableCodeBlock && (
                    <button
                      type="button"
                      onClick={insertCodeBlock}
                      className={cn(
                        "toolbar-button",
                        editor.isActive("codeBlock") && "toolbar-button--active"
                      )}
                      title="Bloco de Código"
                      disabled={disabled}
                    >
                      <Terminal className="w-4 h-4" />
                    </button>
                  )}

                  {/* Steps button */}
                  {enableSteps && (
                    <button
                      type="button"
                      onClick={insertSteps}
                      className={cn(
                        "toolbar-button",
                        editor.isActive("steps") && "toolbar-button--active"
                      )}
                      title="Lista de Passos"
                      disabled={disabled}
                    >
                      <ListChecks className="w-4 h-4" />
                    </button>
                  )}

                  {/* Divider */}
                  <div className="toolbar-divider" />

                  <button
                    type="button"
                    onClick={() =>
                      editor.chain().focus().toggleBulletList().run()
                    }
                    className={cn(
                      "toolbar-button",
                      editor.isActive("bulletList") && "toolbar-button--active"
                    )}
                    title="Lista com marcadores"
                    disabled={disabled}
                  >
                    <List className="w-4 h-4" />
                  </button>
                  <button
                    type="button"
                    onClick={() =>
                      editor.chain().focus().toggleOrderedList().run()
                    }
                    className={cn(
                      "toolbar-button",
                      editor.isActive("orderedList") && "toolbar-button--active"
                    )}
                    title="Lista numerada"
                    disabled={disabled}
                  >
                    <ListOrdered className="w-4 h-4" />
                  </button>
                </>
              )}
            </div>
          )}

          {/* Enhanced Simple Toolbar for Minimal Variant */}
          {showToolbar && variant === "minimal" && (
            <div className="rich-text-input-toolbar rich-text-input-toolbar--minimal">
              <button
                type="button"
                onClick={() => editor.chain().focus().toggleBold().run()}
                className={cn(
                  "toolbar-button toolbar-button--small",
                  editor.isActive("bold") && "toolbar-button--active"
                )}
                title="Negrito (Ctrl+B)"
                disabled={disabled}
              >
                <Bold className="w-3 h-3" />
              </button>
              <button
                type="button"
                onClick={() => editor.chain().focus().toggleItalic().run()}
                className={cn(
                  "toolbar-button toolbar-button--small",
                  editor.isActive("italic") && "toolbar-button--active"
                )}
                title="Itálico (Ctrl+I)"
                disabled={disabled}
              >
                <Italic className="w-3 h-3" />
              </button>
              <button
                type="button"
                onClick={() => editor.chain().focus().toggleCode().run()}
                className={cn(
                  "toolbar-button toolbar-button--small",
                  editor.isActive("code") && "toolbar-button--active"
                )}
                title="Código"
                disabled={disabled}
              >
                <Code className="w-3 h-3" />
              </button>
              {enableLink && (
                <button
                  type="button"
                  onClick={handleLinkClick}
                  className={cn(
                    "toolbar-button toolbar-button--small",
                    editor.isActive("link") && "toolbar-button--active"
                  )}
                  title="Link"
                  disabled={disabled}
                >
                  <LinkIcon className="w-3 h-3" />
                </button>
              )}
            </div>
          )}

          {/* Helper Text */}
          {helperText && (
            <div
              id={`${id}-helper`}
              className={cn(
                "rich-text-input-helper",
                error && "rich-text-input-helper--error"
              )}
            >
              {helperText}
            </div>
          )}
        </div>

        {/* Link Dialog */}
        <LinkDialog
          isOpen={isLinkDialogOpen}
          onClose={() => setIsLinkDialogOpen(false)}
          onConfirm={handleLinkConfirm}
          initialText={linkDialogData.selectedText}
        />
      </>
    );
  }
);

RichTextInput.displayName = "RichTextInput";

export default RichTextInput;
