import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";

export interface OrganizationImage {
  id: string;
  organization_id: string;
  project_id: number;
  image_name: string;
  image_path: string;
  image_url: string;
  file_size: number;
  content_type: string;
  alt_text: string;
  tags: string[];
  metadata: Record<string, unknown>;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface ImageStats {
  total_images: number;
  total_size_mb: number;
  images_by_project: Array<{
    project_name: string;
    image_count: number;
    total_size_mb: number;
  }>;
  most_used_tags: string[];
}

export interface UseOrganizationImagesProps {
  projectId?: number;
  organizationId: string;
  autoLoad?: boolean;
}

export function useOrganizationImages({
  projectId,
  organizationId,
  autoLoad = true,
}: UseOrganizationImagesProps) {
  const [images, setImages] = useState<OrganizationImage[]>([]);
  const [stats, setStats] = useState<ImageStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get Supabase credentials and session
  const getSupabaseHeaders = useCallback(async () => {
    const supabase = createClient();
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;

    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      throw new Error("User not authenticated");
    }

    if (!supabaseUrl) {
      throw new Error("Supabase URL not found");
    }

    return {
      supabaseUrl,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.access_token}`,
        apikey: session.access_token,
      },
    };
  }, []);

  // Load images for a specific project
  const loadProjectImages = useCallback(
    async (
      pProjectId: number,
      tags?: string[],
      searchTerm?: string,
      limit = 100,
      offset = 0
    ) => {
      setLoading(true);
      setError(null);

      try {
        const { supabaseUrl, headers } = await getSupabaseHeaders();

        const response = await fetch(
          `${supabaseUrl}/rest/v1/rpc/get_project_images`,
          {
            method: "POST",
            headers,
            body: JSON.stringify({
              p_project_id: pProjectId,
              p_tags: tags && tags.length > 0 ? tags : null,
              p_search_term: searchTerm || null,
              p_limit: limit,
              p_offset: offset,
            }),
          }
        );

        if (!response.ok) {
          throw new Error(`Failed to load images: ${response.statusText}`);
        }

        const data = (await response.json()) as OrganizationImage[];
        setImages(data);
        return data;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to load images";
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [getSupabaseHeaders]
  );

  // Load organization statistics
  const loadOrganizationStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const { supabaseUrl, headers } = await getSupabaseHeaders();

      const response = await fetch(
        `${supabaseUrl}/rest/v1/rpc/get_organization_image_stats`,
        {
          method: "POST",
          headers,
          body: JSON.stringify({
            p_organization_id: organizationId,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to load stats: ${response.statusText}`);
      }

      const data = (await response.json()) as ImageStats[];
      const statsData = data[0] || {
        total_images: 0,
        total_size_mb: 0,
        images_by_project: [],
        most_used_tags: [],
      };

      setStats(statsData);
      return statsData;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load statistics";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [organizationId, getSupabaseHeaders]);

  // Delete an image
  const deleteImage = useCallback(
    async (imageId: string) => {
      try {
        const { supabaseUrl, headers } = await getSupabaseHeaders();

        // First, get the image details to delete from storage
        const imageToDelete = images.find((img) => img.id === imageId);
        if (!imageToDelete) {
          throw new Error("Image not found");
        }

        // Delete from storage
        const storageResponse = await fetch(
          `${supabaseUrl}/storage/v1/object/organization-images/${imageToDelete.image_path}`,
          {
            method: "DELETE",
            headers: {
              Authorization: headers.Authorization,
            },
          }
        );

        if (!storageResponse.ok) {
          console.warn(
            "Failed to delete from storage, but continuing with database deletion"
          );
        }

        // Delete from database
        const dbResponse = await fetch(
          `${supabaseUrl}/rest/v1/organization_images?id=eq.${imageId}`,
          {
            method: "DELETE",
            headers,
          }
        );

        if (!dbResponse.ok) {
          throw new Error(`Failed to delete image: ${dbResponse.statusText}`);
        }

        // Update local state
        setImages((prev) => prev.filter((img) => img.id !== imageId));

        return true;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to delete image";
        setError(errorMessage);
        throw err;
      }
    },
    [images, getSupabaseHeaders]
  );

  // Update image metadata
  const updateImageMetadata = useCallback(
    async (
      imageId: string,
      updates: Partial<
        Pick<OrganizationImage, "alt_text" | "tags" | "metadata">
      >
    ) => {
      try {
        const { supabaseUrl, headers } = await getSupabaseHeaders();

        const response = await fetch(
          `${supabaseUrl}/rest/v1/organization_images?id=eq.${imageId}`,
          {
            method: "PATCH",
            headers,
            body: JSON.stringify({
              ...updates,
              updated_at: new Date().toISOString(),
            }),
          }
        );

        if (!response.ok) {
          throw new Error(`Failed to update image: ${response.statusText}`);
        }

        // Update local state
        setImages((prev) =>
          prev.map((img) =>
            img.id === imageId
              ? { ...img, ...updates, updated_at: new Date().toISOString() }
              : img
          )
        );

        return true;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update image";
        setError(errorMessage);
        throw err;
      }
    },
    [getSupabaseHeaders]
  );

  // Cleanup orphaned images
  const cleanupOrphanedImages = useCallback(async () => {
    try {
      const { supabaseUrl, headers } = await getSupabaseHeaders();

      const response = await fetch(
        `${supabaseUrl}/rest/v1/rpc/cleanup_orphaned_images`,
        {
          method: "POST",
          headers,
          body: JSON.stringify({}),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to cleanup images: ${response.statusText}`);
      }

      const result = (await response.json()) as Array<{
        cleaned_count: number;
        details: string;
      }>;
      return (
        result[0] || { cleaned_count: 0, details: "No orphaned images found" }
      );
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to cleanup images";
      setError(errorMessage);
      throw err;
    }
  }, [getSupabaseHeaders]);

  // Auto-load on mount if enabled
  useEffect(() => {
    if (autoLoad && projectId) {
      loadProjectImages(projectId);
    }
  }, [autoLoad, projectId, loadProjectImages]);

  // Auto-load stats on mount if enabled
  useEffect(() => {
    if (autoLoad) {
      loadOrganizationStats();
    }
  }, [autoLoad, loadOrganizationStats]);

  return {
    // State
    images,
    stats,
    loading,
    error,

    // Actions
    loadProjectImages,
    loadOrganizationStats,
    deleteImage,
    updateImageMetadata,
    cleanupOrphanedImages,

    // Utils
    refresh: () => {
      if (projectId) {
        loadProjectImages(projectId);
      }
      loadOrganizationStats();
    },
    clearError: () => setError(null),
  };
}
