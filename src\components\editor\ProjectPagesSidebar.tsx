"use client";

import React, { useState, useEffect, useCallback, useTransition } from "react";
import { usePathname } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
import FileTree from "./sidebar/FileTree";
import FileTreeSkeleton from "./sidebar/FileTreeSkeleton";
// import ConfigJsonPreview from "./ConfigJsonPreview";
import type { TreeNode } from "./sidebar/types";
import type { ConfigJson, DocusaurusPageGroup } from "@/types/sidebarConfig";
import {
  convertConfigToTree,
  convertTreeToConfig,
  findNodeById,
  removeNodeById,
  insertNode,
  isDescendant,
  updateAllPaths,
  collectDescendantIds,
  buildPathFromHierarchy,
  // cleanupGroupMainPages,
  // reorganizeGroupStructures,
} from "@/utils/fileTreeUtils";
import { useToast } from "@/components/ToastProvider";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Upload,
  Menu,
  Minus,
  ChevronDown,
  ChevronRight,
  Zap,
} from "lucide-react";
import { ApiUploadModal } from "./ApiUploadModal";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ProjectPagesSidebarProps {
  projectId: string;
}

export default function ProjectPagesSidebar({
  projectId,
}: ProjectPagesSidebarProps) {
  const [tree, setTree] = useState<TreeNode[]>([]);
  const [config, setConfig] = useState<ConfigJson | null>(null);
  const [, setLiveConfig] = useState<ConfigJson | null>(null);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  const [clickedPageHref, setClickedPageHref] = useState<string | null>(null);
  const [isApiUploadOpen, setIsApiUploadOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false); // Estado para animação de atualização
  const [isQuickActionsExpanded, setIsQuickActionsExpanded] = useState(false);

  const {
    selectedProject,
    isLoading: isProjectLoading,
    setSelectedProject,
  } = useProject();
  const pathname = usePathname();
  const supabase = createClient();
  const { addToast } = useToast();

  const saveExpandedState = (expanded: Set<string>) => {
    try {
      const expandedArray = Array.from(expanded);
      localStorage.setItem(
        `expanded-nodes-${projectId}`,
        JSON.stringify(expandedArray)
      );
      console.log("💾 Expanded state saved:", expandedArray);
    } catch (error) {
      console.error("❌ Error saving expanded state:", error);
    }
  };

  const loadExpandedState = (): Set<string> => {
    try {
      const saved = localStorage.getItem(`expanded-nodes-${projectId}`);
      if (saved) {
        const expandedArray = JSON.parse(saved) as string[];
        const expandedSet = new Set(expandedArray);
        console.log("📂 Expanded state loaded:", expandedArray);
        return expandedSet;
      }
    } catch (error) {
      console.error("❌ Error loading expanded state:", error);
    }
    return new Set();
  };

  const collectExpandedNodes = (nodes: TreeNode[]): Set<string> => {
    const expandedNodes = new Set<string>();
    const collect = (nodeList: TreeNode[]) => {
      nodeList.forEach((node) => {
        if (node.isExpanded) {
          expandedNodes.add(node.id);
        }
        if (node.children) {
          collect(node.children);
        }
      });
    };
    collect(nodes);
    return expandedNodes;
  };

  const processSidebarConfig = useCallback(() => {
    if (!selectedProject?.configjson) {
      console.log(
        "No configjson found for this project. Displaying empty tree."
      );
      setConfig(null);
      setTree([]);
      return;
    }

    const configData = selectedProject.configjson as ConfigJson;

    // Verificar se o config realmente mudou - comparando apenas sidebars e navbar
    const configChanged =
      config === null ||
      JSON.stringify(config.sidebars) !== JSON.stringify(configData.sidebars) ||
      JSON.stringify(config.navbar) !== JSON.stringify(configData.navbar);

    if (!configChanged) {
      console.log("📊 Sidebars/navbar haven't changed, skipping tree update");
      // Ainda assim, atualizar o config para manter outras propriedades sincronizadas
      setConfig(configData);
      setLiveConfig(configData);
      return;
    }

    console.log("🔄 Sidebars/navbar changed, updating tree...");

    // Mostrar animação de atualização
    setIsUpdating(true);

    setConfig(configData);
    setLiveConfig(configData);

    if (configData.sidebars && Array.isArray(configData.sidebars)) {
      const savedExpanded = loadExpandedState();
      const treeData = convertConfigToTree(configData, savedExpanded);

      // Usar transition para atualização suave
      startTransition(() => {
        setTree(treeData);
        // Remover animação após um pequeno delay
        setTimeout(() => {
          setIsUpdating(false);
        }, 300);
      });
    } else {
      console.error(
        "No sidebars array found in configjson or it's not an array"
      );
      setTree([]);
      setIsUpdating(false);
    }
  }, [selectedProject, config]);

  // Force refresh project data from database
  const forceRefreshProject = useCallback(async () => {
    try {
      console.log("🔄 Force refreshing project data from database...");

      const { data: projectData, error } = await supabase
        .from("projects")
        .select("*")
        .eq("id", parseInt(projectId, 10))
        .single();

      if (error) {
        console.error("❌ Error fetching fresh project data:", error);
        return;
      }

      if (projectData) {
        console.log("✅ Fresh project data loaded:", projectData);

        // Update the selected project in context if it matches
        if (selectedProject && selectedProject.id === projectData.id) {
          setSelectedProject({ ...selectedProject, ...projectData });
        }

        // Process the fresh config data
        const configData = projectData.configjson as ConfigJson;
        if (configData) {
          setConfig(configData);
          setLiveConfig(configData);

          if (configData.sidebars && Array.isArray(configData.sidebars)) {
            const savedExpanded = loadExpandedState();
            const treeData = convertConfigToTree(configData, savedExpanded);
            startTransition(() => {
              setTree(treeData);
            });
            console.log("✅ Tree updated with fresh data");
          }
        }
      }
    } catch (error) {
      console.error("❌ Error in forceRefreshProject:", error);
    }
  }, [projectId, selectedProject, supabase]);

  useEffect(() => {
    if (projectId && !isProjectLoading) {
      processSidebarConfig();
    }
  }, [projectId, isProjectLoading, processSidebarConfig]);

  useEffect(() => {
    if (clickedPageHref && pathname === clickedPageHref) {
      setClickedPageHref(null);
    }
  }, [pathname, clickedPageHref]);

  const handlePageClick = useCallback(
    (href: string) => {
      if (pathname === href && !clickedPageHref) return;
      setClickedPageHref(href);
    },
    [pathname, clickedPageHref]
  );

  const createPageLink = useCallback(
    (pagePath: string): string => {
      const basePath = `/${projectId}/editor`;
      const normalizedPagePath = pagePath.startsWith("/")
        ? pagePath.substring(1)
        : pagePath;
      return normalizedPagePath
        ? `${basePath}/${normalizedPagePath}`
        : basePath;
    },
    [projectId]
  );

  const getItemClasses = useCallback(
    (href: string): string => {
      const isTrulyActive = pathname === href;
      const isClickedAndLoading = clickedPageHref === href && pathname !== href;

      if (isTrulyActive) {
        return "bg-blue-100 dark:bg-blue-700 text-blue-700 dark:text-blue-100 font-medium";
      }
      if (isClickedAndLoading) {
        return "bg-blue-50 dark:bg-blue-700/40 text-blue-600 dark:text-blue-300 font-normal animate-pulse";
      }
      return "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700";
    },
    [pathname, clickedPageHref]
  );

  const toggleNode = (id: string) => {
    const updateNode = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.map((node) => {
        if (node.id === id) {
          const newExpanded = !node.isExpanded;
          return { ...node, isExpanded: newExpanded };
        }
        if (node.children) {
          return { ...node, children: updateNode(node.children) };
        }
        return node;
      });
    };

    const updatedTree = updateNode(tree);
    setTree(updatedTree);

    const expandedNodes = collectExpandedNodes(updatedTree);
    saveExpandedState(expandedNodes);
  };

  const handleDrop = async (
    draggedId: string,
    targetId: string,
    position: "inside" | "before" | "after"
  ) => {
    const draggedNode = findNodeById(tree, draggedId);
    const targetNode = findNodeById(tree, targetId);

    if (!draggedNode || !targetNode) return;

    if (draggedId === targetId) return;
    if (isDescendant(draggedId, targetId, tree)) return;

    if (position === "inside" && draggedId === targetId) return;

    console.log(
      `🔄 Trying to move ${draggedNode.type} to ${targetNode.type} (position: ${position})`
    );

    if (
      (draggedNode.type === "page" || draggedNode.type === "subpage") &&
      position === "inside" &&
      !(targetNode.type === "category" || targetNode.type === "group")
    ) {
      console.log("❌ Files can only go INSIDE categories or groups");
      return;
    }

    // Remover validações antigas - agora são tratadas no FileTreeItem

    if (draggedNode.type === "navbar") {
      console.log(`🔍 Analyzing movement of navbar item:`, {
        draggedId: draggedNode.id,
        draggedType: draggedNode.data?.type,
        draggedDropdownIndex: draggedNode.data?.dropdownIndex,
        targetId: targetNode.id,
        targetType: targetNode.data?.type,
        targetDropdownIndex: targetNode.data?.dropdownIndex,
        position,
      });

      // Permitir navbar items de raiz irem para dentro de dropdowns
      if (
        position === "inside" &&
        draggedNode.data?.type === "navbar-item" &&
        targetNode.type === "navbar" &&
        targetNode.data?.type === "navbar-dropdown"
      ) {
        console.log("✅ Allowing navbar item to move inside dropdown");
        // Continuar com o movimento
      } else if (position === "inside" || targetNode.type !== "navbar") {
        console.log(
          "❌ Navbar items can only be reordered among themselves or moved into dropdowns"
        );
        return;
      } else {
        // Validações para movimentos before/after
        const draggedIsDropdownItem =
          draggedNode.data?.type === "navbar-dropdown-item";
        const targetIsDropdownItem =
          targetNode.data?.type === "navbar-dropdown-item";

        console.log(`🔍 Checking types:`, {
          draggedIsDropdownItem,
          targetIsDropdownItem,
        });

        if (draggedIsDropdownItem) {
          if (
            !targetIsDropdownItem ||
            draggedNode.data?.dropdownIndex !== targetNode.data?.dropdownIndex
          ) {
            console.log(
              "❌ Dropdown navbar items can only be reordered within the same dropdown"
            );
            return;
          }
        }

        if (targetIsDropdownItem) {
          if (
            !draggedIsDropdownItem ||
            draggedNode.data?.dropdownIndex !== targetNode.data?.dropdownIndex
          ) {
            console.log(
              "❌ Dropdown navbar items can only swap positions with items from the same dropdown"
            );
            return;
          }
        }

        console.log("✅ Allowing reordering of navbar items at the same level");
      }
    }

    if (position !== "inside") {
      const isTargetRootLevel = !findNodeById(tree, targetNode.parentId || "");
      if (isTargetRootLevel) {
        if (
          draggedNode.type !== "navbar" ||
          draggedNode.data?.type === "navbar-dropdown-item"
        ) {
          console.log(
            "❌ It's not allowed to move items to the FileTree root (except for root navbar items)"
          );
          return;
        }
      }

      const targetParent = findNodeById(tree, targetNode.parentId || "");
      if (targetParent && targetParent.type === "navbar") {
        if (draggedNode.type === "page" || draggedNode.type === "subpage") {
          console.log(
            "❌ before/after move would result in a file as a direct child of a navbar"
          );
          return;
        }
      }
    }

    console.log("✅ Validations passed, executing move...");

    let newTree = removeNodeById(tree, draggedId);

    let nodeToInsert = { ...draggedNode };

    if (
      draggedNode.type === "sidebar" &&
      (targetNode.type === "category" || targetNode.type === "group") &&
      position === "inside"
    ) {
      nodeToInsert = transformSidebarToGroup(draggedNode);
    } else if (
      draggedNode.type === "category" &&
      (targetNode.type === "category" || targetNode.type === "group") &&
      position === "inside"
    ) {
      nodeToInsert = transformCategoryToGroup(draggedNode);
    } else if (
      draggedNode.type === "navbar" &&
      draggedNode.data?.type === "navbar-item" &&
      targetNode.type === "navbar" &&
      targetNode.data?.type === "navbar-dropdown" &&
      position === "inside"
    ) {
      // Transformar navbar item em navbar-dropdown-item quando movido para dentro de dropdown
      nodeToInsert = {
        ...draggedNode,
        data: {
          ...draggedNode.data,
          type: "navbar-dropdown-item",
          dropdownIndex: targetNode.data?.index || 0,
        },
      };
      console.log("🔄 Transforming navbar-item to navbar-dropdown-item");
    }

    if (position === "before" || position === "after") {
      const targetIndex = newTree.findIndex((node) => node.id === targetId);
      if (targetIndex !== -1) {
        const insertIndex =
          position === "before" ? targetIndex : targetIndex + 1;
        newTree.splice(insertIndex, 0, {
          ...nodeToInsert,
          parentId: undefined,
        });

        // Coletar IDs afetados: nó movido e seus descendentes
        const affectedIds = collectDescendantIds(nodeToInsert);

        const updatedTree = updateAllPaths(newTree, affectedIds);
        setTree(updatedTree);

        await saveConfigToSupabase(updatedTree);
        return;
      }
    }

    newTree = insertNode(newTree, targetId, nodeToInsert, position);

    // Processar transformações de navbar items após inserção
    newTree = processNodesForNavbarTransformation(newTree);

    // Coletar IDs afetados: nó movido, seus descendentes e o nó alvo
    const affectedIds = new Set<string>();
    // Adicionar o nó movido e seus descendentes
    const movedNodeIds = collectDescendantIds(nodeToInsert);
    movedNodeIds.forEach((id) => affectedIds.add(id));
    // Adicionar o nó alvo (que pode ter sido expandido)
    affectedIds.add(targetId);

    const updatedTree = updateAllPaths(newTree, affectedIds);
    setTree(updatedTree);

    console.log("📊 Tree state after drop:");
    console.log(JSON.stringify(updatedTree, null, 2));

    await saveConfigToSupabase(updatedTree);
  };

  const saveConfigToSupabase = async (currentTree: TreeNode[]) => {
    if (!config || !selectedProject) {
      console.log("❌ Save cancelled: config or selectedProject missing");
      console.log("config:", !!config);
      console.log("selectedProject:", !!selectedProject);
      return;
    }

    console.log("🔄 Starting save...");
    console.log("Project ID:", projectId);
    console.log("Selected Project ID:", selectedProject.id);

    const currentExpandedNodes = collectExpandedNodes(currentTree);
    console.log(
      "💾 Preserving expanded state:",
      Array.from(currentExpandedNodes)
    );

    try {
      const newConfig = convertTreeToConfig(currentTree, config || undefined);
      console.log("📝 New configuration generated:", newConfig);

      await updateFilePathsIfNeeded(config, newConfig);

      console.log("💾 Saving configjson to projects table...");
      const projectIdNumber = parseInt(projectId, 10);
      console.log("🔢 Project ID converted to number:", projectIdNumber);

      const { data, error } = await supabase
        .from("projects")
        .update({ configjson: newConfig })
        .eq("id", projectIdNumber)
        .select();

      if (error) {
        console.error("❌ Error saving config:", error);
        console.error(
          "Error details:",
          error.message,
          error.details,
          error.hint
        );
      } else {
        console.log("✅ Data saved to Supabase:", data);

        if (data && data.length === 0) {
          console.warn(
            "⚠️ Update executed but no record was affected. This may indicate RLS permission issues."
          );
        }

        setConfig(newConfig);
        setLiveConfig(newConfig);

        const newTreeData = convertConfigToTree(
          newConfig,
          currentExpandedNodes
        );
        setTree(newTreeData);

        saveExpandedState(currentExpandedNodes);

        console.log(
          "✅ Configuration saved automatically with expanded state preserved!"
        );

        addToast("Configuration saved automatically", "success", "File Tree");

        setTimeout(() => {}, 2000);
      }
    } catch (error) {
      console.error("❌ Error converting tree to config:", error);
    }
  };

  const generateUniqueId = (prefix: string): string => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  const updateNodeIds = (node: TreeNode, parentId?: string): TreeNode => {
    const newId = generateUniqueId(node.type);
    return {
      ...node,
      id: newId,
      parentId: parentId,
      children:
        node.children?.map((child) => updateNodeIds(child, newId)) || [],
    };
  };

  const transformSidebarToGroup = (sidebarNode: TreeNode): TreeNode => {
    const transformed = {
      ...sidebarNode,
      type: "group" as const,
      data: {
        ...sidebarNode.data,
        type: "group",
        groupName: sidebarNode.name,
        page: "",
      },
      children:
        sidebarNode.children?.map((child) =>
          child.type === "category" ? transformCategoryToGroup(child) : child
        ) || [],
    };

    return updateNodeIds(transformed);
  };

  const transformCategoryToGroup = (categoryNode: TreeNode): TreeNode => {
    const transformed = {
      ...categoryNode,
      type: "group" as const,
      data: {
        ...categoryNode.data,
        type: "group",
        groupName: categoryNode.data.categoryName || categoryNode.name,
        page: "",
      },
      // IMPORTANTE: Preservar os filhos da categoria
      children: categoryNode.children || [],
    };

    return updateNodeIds(transformed);
  };

  const updateFilePathsIfNeeded = async (
    oldConfig: ConfigJson,
    newConfig: ConfigJson
  ) => {
    console.log("🔍 Checking for path changes...");

    const oldPaths = extractAllPaths(oldConfig);
    const newPaths = extractAllPaths(newConfig);

    console.log("📁 Old paths:", oldPaths);
    console.log("📁 New paths:", newPaths);

    const pathMappings = detectPathChanges(oldPaths, newPaths);

    if (pathMappings.length === 0) {
      console.log("✅ No paths were changed");
      return;
    }

    console.log("🔄 Paths that changed:", pathMappings);

    for (const mapping of pathMappings) {
      console.log(`💾 Updating path: ${mapping.oldPath} -> ${mapping.newPath}`);

      const { data, error } = await supabase
        .from("project_pages")
        .update({ path: mapping.newPath })
        .eq("project_id", parseInt(projectId, 10))
        .eq("path", mapping.oldPath)
        .select();

      if (error) {
        console.error(
          `❌ Error updating path ${mapping.oldPath} -> ${mapping.newPath}:`,
          error
        );
      } else {
        console.log(
          `✅ Path updated: ${mapping.oldPath} -> ${mapping.newPath}`,
          data
        );
      }
    }
  };

  const extractAllPaths = (config: ConfigJson): string[] => {
    const paths: string[] = [];

    config.sidebars.forEach((sidebar) => {
      sidebar.categories.forEach((category) => {
        category.pages.forEach((page) => {
          if (typeof page === "string") {
            paths.push(page);
          } else {
            if (page.page) {
              paths.push(page.page);
            }
            page.subpages.forEach((subpage) => {
              if (typeof subpage === "string") {
                paths.push(subpage);
              } else {
                paths.push(...extractPathsFromGroup(subpage));
              }
            });
          }
        });
      });
    });

    return paths;
  };

  const extractPathsFromGroup = (group: DocusaurusPageGroup): string[] => {
    const paths: string[] = [];

    if (group.page) {
      paths.push(group.page);
    }

    if (group.subpages) {
      group.subpages.forEach((subpage: string | DocusaurusPageGroup) => {
        if (typeof subpage === "string") {
          paths.push(subpage);
        } else {
          paths.push(...extractPathsFromGroup(subpage));
        }
      });
    }

    return paths;
  };

  const detectPathChanges = (
    oldPaths: string[],
    newPaths: string[]
  ): Array<{ oldPath: string; newPath: string }> => {
    const mappings: Array<{ oldPath: string; newPath: string }> = [];

    const disappearedPaths = oldPaths.filter(
      (path) => !newPaths.includes(path)
    );
    const appearedPaths = newPaths.filter((path) => !oldPaths.includes(path));

    disappearedPaths.forEach((oldPath) => {
      const fileName = oldPath.split("/").pop() || "";

      const matchingNewPath = appearedPaths.find((newPath) => {
        const newFileName = newPath.split("/").pop() || "";
        return newFileName === fileName;
      });

      if (matchingNewPath) {
        mappings.push({
          oldPath: oldPath,
          newPath: matchingNewPath,
        });

        const appearedIndex = appearedPaths.indexOf(matchingNewPath);
        if (appearedIndex > -1) {
          appearedPaths.splice(appearedIndex, 1);
        }
      }
    });

    return mappings;
  };

  const renderSidebarContent = () => {
    // Não mostrar skeleton se já temos dados na árvore - apenas na primeira carga
    if ((isProjectLoading || isPending) && tree.length === 0) {
      return <FileTreeSkeleton />;
    }

    // Nó virtual raiz para os Quick Actions
    const rootNode: TreeNode = {
      id: "root",
      name: "Root",
      type: "navbar" as const,
      parentId: undefined,
      children: tree,
      isExpanded: true,
      data: { type: "root" },
    };

    return (
      <div className="flex flex-col h-full">
        {/* Seção de ações expansível */}
        <div className="border-b border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-900/50 flex-shrink-0">
          {/* Header sempre visível */}
          <div
            className="flex items-center justify-between px-4 py-2 cursor-pointer hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-colors"
            onClick={() => {
              const newState = !isQuickActionsExpanded;
              setIsQuickActionsExpanded(newState);
            }}
          >
            <div className="flex items-center gap-2">
              <Zap className="h-3.5 w-3.5 text-yellow-600 dark:text-yellow-400" />
              <span className="text-xs text-gray-600 dark:text-gray-400 font-medium uppercase tracking-wider">
                Quick Actions
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/* Ícone de expansão */}
              {isQuickActionsExpanded ? (
                <ChevronDown className="h-3.5 w-3.5 text-gray-400 dark:text-gray-500 transition-transform duration-200" />
              ) : (
                <ChevronRight className="h-3.5 w-3.5 text-gray-400 dark:text-gray-500 transition-transform duration-200" />
              )}
            </div>
          </div>

          {/* Conteúdo expansível */}
          <div
            className={cn(
              "overflow-hidden transition-all duration-300 ease-in-out",
              isQuickActionsExpanded
                ? "max-h-96 opacity-100"
                : "max-h-0 opacity-0"
            )}
          >
            <div className="px-4 pb-3 pt-2 space-y-3">
              {/* Grupo de ações de navegação */}
              <div className="space-y-2">
                <div className="text-xs text-gray-400 dark:text-gray-500">
                  Navigation
                </div>
                <div className="grid grid-cols-2 gap-2">
                  {/* Botão Navbar Dropdown */}
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 px-3 text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-400 hover:border-blue-300 dark:hover:border-blue-700 transition-all duration-200 flex items-center justify-center group"
                    onClick={() =>
                      handleCreateItem(
                        rootNode,
                        "navbar-dropdown",
                        "New Dropdown"
                      )
                    }
                    title="Create a dropdown menu in the navigation bar"
                  >
                    <Menu className="h-3.5 w-3.5 mr-1.5 text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300" />
                    Dropdown
                  </Button>

                  {/* Botão Navbar Item */}
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 px-3 text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-orange-50 dark:hover:bg-orange-900/20 hover:text-orange-700 dark:hover:text-orange-400 hover:border-orange-300 dark:hover:border-orange-700 transition-all duration-200 flex items-center justify-center group"
                    onClick={() =>
                      handleCreateItem(rootNode, "navbar-item", "New Nav Item")
                    }
                    title="Create a navigation item"
                  >
                    <Minus className="h-3.5 w-3.5 mr-1.5 text-orange-600 dark:text-orange-400 group-hover:text-orange-700 dark:group-hover:text-orange-300" />
                    Nav Item
                  </Button>
                </div>
              </div>

              {/* Separador visual */}
              <div className="border-t border-gray-200/50 dark:border-gray-700/50"></div>

              {/* Grupo de importação */}
              <div className="space-y-2">
                <div className="text-xs text-gray-400 dark:text-gray-500">
                  Import
                </div>
                {/* Botão Import API */}
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full h-8 px-3 text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-700 dark:hover:text-purple-400 hover:border-purple-300 dark:hover:border-purple-700 transition-all duration-200 flex items-center justify-center group"
                  onClick={() => setIsApiUploadOpen(true)}
                  title="Import API documentation"
                >
                  <Upload className="h-3.5 w-3.5 mr-1.5 text-purple-600 dark:text-purple-400 group-hover:text-purple-700 dark:group-hover:text-purple-300" />
                  Import API Docs
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Container do FileTree que ocupa toda a altura restante */}
        <div
          className={cn(
            "relative flex-1 transition-all duration-300",
            // Transição suave de opacidade quando está atualizando
            (isUpdating || isPending) && "opacity-60"
          )}
        >
          <FileTree
            tree={tree}
            onToggle={toggleNode}
            onDrop={handleDrop}
            draggedItem={draggedItem}
            setDraggedItem={setDraggedItem}
            createPageLink={createPageLink}
            handlePageClick={handlePageClick}
            getItemClasses={getItemClasses}
            onCreateItem={handleCreateItem}
            onRenameItem={handleRenameItem}
            onDeleteItem={handleDeleteItem}
            editingNodeId={editingNodeId}
            setEditingNodeId={setEditingNodeId}
          />

          {/* Indicador de atualização*/}
          {(isUpdating || isPending) && (
            <div className="absolute top-2 right-2 pointer-events-none">
              <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-md flex items-center gap-2 border border-gray-200/50 dark:border-gray-700/50">
                <div className="animate-spin rounded-full h-3 w-3 border-2 border-gray-300 border-t-blue-600 dark:border-gray-600 dark:border-t-blue-400"></div>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  Atualizando
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const handleCreateItem = async (
    parentNode: TreeNode,
    type: "category" | "group" | "page" | "navbar-item" | "navbar-dropdown",
    name: string,
    path?: string
  ) => {
    console.log(`🆕 Creating ${type}: ${name} in ${parentNode.name}`);

    const newId = `${type}-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    let finalPath = path;

    if (type === "page" && !finalPath) {
      const parentPath = buildPathFromHierarchy(parentNode.id, tree);
      finalPath = parentPath
        ? `${parentPath}/${name.toLowerCase().replace(/\s+/g, "-")}`
        : name.toLowerCase().replace(/\s+/g, "-");
    }

    let sidebarRef = "";
    if (type === "navbar-item") {
      sidebarRef = `sidebar-${name
        .toLowerCase()
        .replace(/\s+/g, "-")}-${Date.now()}`;
    }

    const newNode: TreeNode = {
      id: newId,
      name,
      type: type.startsWith("navbar") ? "navbar" : (type as TreeNode["type"]),
      parentId: parentNode.data?.type === "root" ? undefined : parentNode.id,
      children: type !== "page" ? [] : undefined,
      isExpanded: type !== "page",
      data: {
        type:
          type === "navbar-item"
            ? parentNode.data?.type === "navbar-dropdown"
              ? "navbar-dropdown-item"
              : "navbar-item"
            : type,
        ...(type === "category" && { categoryName: name }),
        ...(type === "group" && { groupName: name }),
        ...(type === "page" && {
          page: finalPath || name,
          path: finalPath || name,
        }),
        ...(type === "navbar-dropdown" && { label: name }),
        ...(type === "navbar-item" && {
          label: name,
          sidebarRef: sidebarRef,
          ...(parentNode.data?.type === "navbar-dropdown" && {
            dropdownIndex: parentNode.data?.index || 0,
          }),
        }),
      },
      ...(finalPath && { path: finalPath }),
    };

    const addNodeToTree = (nodes: TreeNode[]): TreeNode[] => {
      if (parentNode.data?.type === "root") {
        return [...nodes, newNode];
      }

      return nodes.map((node) => {
        if (node.id === parentNode.id) {
          return {
            ...node,
            children: [...(node.children || []), newNode],
            isExpanded: true,
          };
        }
        if (node.children) {
          return {
            ...node,
            children: addNodeToTree(node.children),
            isExpanded: node.isExpanded,
          };
        }
        return node;
      });
    };

    const updatedTree = addNodeToTree(tree);
    setTree(updatedTree);

    // Log para debug do estado de expansão
    console.log("🔍 Estado de expansão após criação:", {
      totalNodes: updatedTree.length,
      expandedNodes: collectExpandedNodes(updatedTree),
    });

    if (type === "page" && finalPath) {
      try {
        console.log(`💾 Creating page in Supabase: ${finalPath}`);

        const { data: insertData, error: insertError } = await supabase
          .from("project_pages")
          .insert({
            project_id: parseInt(projectId, 10),
            path: finalPath,
            title: name,
            content: `<h1>${name}</h1><p>This is a new page. Start writing!</p>`,
          })
          .select()
          .single();

        if (insertError) {
          console.error("❌ Error creating page in Supabase:", insertError);
        } else {
          console.log("✅ Page created in Supabase:", insertData);
        }
      } catch (error) {
        console.error("❌ Unexpected error creating page:", error);
      }
    }

    if (type === "navbar-item" && sidebarRef) {
      console.log(`📁 Creating empty sidebar for navbar item: ${sidebarRef}`);
    }

    await saveConfigToSupabase(updatedTree);
  };

  const handleRenameItem = async (node: TreeNode, newName: string) => {
    console.log(`✏️ Renaming ${node.name} to ${newName} (type: ${node.type})`);
    console.log(
      `🔍 Example: If renaming "Category" to "New Category", all paths like:`
    );
    console.log(`   "Navbar Dropdown/Navbar Item/Category/Page" will become`);
    console.log(`   "Navbar Dropdown/Navbar Item/New Category/Page"`);

    // Coletar todos os paths antigos antes da renomeação (para containers)
    const oldPaths: string[] = [];

    if (
      node.type === "navbar" ||
      node.type === "category" ||
      node.type === "group"
    ) {
      console.log(`📋 Collecting child paths before renaming...`);
      collectAllChildPaths(node, oldPaths);
      console.log(`📋 Paths found: [${oldPaths.join(", ")}]`);
    }

    const renameNodeInTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.map((treeNode) => {
        if (treeNode.id === node.id) {
          const updatedNode = {
            ...treeNode,
            name: newName,
            data: {
              ...treeNode.data,
              ...(treeNode.type === "category" && { categoryName: newName }),
              ...(treeNode.type === "group" && { groupName: newName }),
              ...(treeNode.type === "navbar" && { label: newName }),
            },
          };

          // Para páginas individuais, apenas atualiza o nome do arquivo
          if (treeNode.type === "page" || treeNode.type === "subpage") {
            const newPath = treeNode.path?.replace(
              /[^\/]+$/,
              newName.toLowerCase().replace(/\s+/g, "-")
            );
            updatedNode.path = newPath;
            updatedNode.data.page = newPath;

            if (treeNode.path && newPath) {
              updatePageInSupabase(treeNode.path, newName, newPath);
            }
          }

          return updatedNode;
        }
        if (treeNode.children) {
          return {
            ...treeNode,
            children: renameNodeInTree(treeNode.children),
            isExpanded: treeNode.isExpanded,
          };
        }
        return treeNode;
      });
    };

    const updatedTree = renameNodeInTree(tree);

    // Aplicar updateAllPaths para reconstruir os paths baseados na nova hierarquia
    // Coletar IDs afetados: o nó renomeado e todos seus descendentes
    const affectedIds = collectDescendantIds(node);

    const treeWithUpdatedPaths = updateAllPaths(updatedTree, affectedIds);
    setTree(treeWithUpdatedPaths);

    // Para containers, coletar os novos paths e atualizar no Supabase
    if (
      node.type === "navbar" ||
      node.type === "category" ||
      node.type === "group"
    ) {
      const renamedNode = findNodeById(treeWithUpdatedPaths, node.id);
      if (renamedNode) {
        const newPaths: string[] = [];
        collectAllChildPaths(renamedNode, newPaths);
        console.log(`📋 New paths after renaming: [${newPaths.join(", ")}]`);

        // Atualizar paths no Supabase
        await updatePathsInSupabase(oldPaths, newPaths);
      }
    }

    await saveConfigToSupabase(treeWithUpdatedPaths);
  };

  // Função auxiliar para coletar todos os paths de páginas filhas
  const collectAllChildPaths = (parentNode: TreeNode, pathsArray: string[]) => {
    if (!parentNode.children) return;

    parentNode.children.forEach((child) => {
      // Coletar páginas regulares e subpages
      if ((child.type === "page" || child.type === "subpage") && child.path) {
        pathsArray.push(child.path);
      }

      // Coletar página principal dos groups (field "page" no data)
      if (child.type === "group" && child.data?.page) {
        console.log(`📄 Found group main page: ${child.data.page}`);
        pathsArray.push(child.data.page);
      }

      // Processar recursivamente containers filhos
      if (child.children) {
        collectAllChildPaths(child, pathsArray);
      }
    });
  };

  // Função para atualizar paths no Supabase baseado nos arrays antigos e novos
  const updatePathsInSupabase = async (
    oldPaths: string[],
    newPaths: string[]
  ) => {
    console.log(`🔄 Updating ${oldPaths.length} paths in Supabase...`);
    console.log(`📋 Old paths: [${oldPaths.join(", ")}]`);
    console.log(`📋 New paths: [${newPaths.join(", ")}]`);

    // Mapear paths antigos para novos baseado no nome do arquivo
    const pathMappings: Array<{ oldPath: string; newPath: string }> = [];

    oldPaths.forEach((oldPath) => {
      const fileName = oldPath.split("/").pop() || "";
      console.log(`🔍 Looking for file match: ${fileName}`);

      // Encontrar o novo path correspondente (mesmo nome de arquivo)
      const matchingNewPath = newPaths.find((newPath) => {
        const newFileName = newPath.split("/").pop() || "";
        return newFileName === fileName;
      });

      if (matchingNewPath) {
        console.log(`✅ Match found: ${oldPath} -> ${matchingNewPath}`);
        pathMappings.push({ oldPath, newPath: matchingNewPath });
      } else {
        console.warn(`⚠️ No match found for: ${oldPath} (file: ${fileName})`);
      }
    });

    console.log(`📋 Total mappings found: ${pathMappings.length}`);
    console.log(`📋 Path mappings:`, pathMappings);

    // Atualizar cada path no Supabase
    for (const mapping of pathMappings) {
      try {
        console.log(
          `💾 Updating in Supabase: ${mapping.oldPath} -> ${mapping.newPath}`
        );

        const { error: updateError } = await supabase
          .from("project_pages")
          .update({ path: mapping.newPath })
          .eq("project_id", parseInt(projectId, 10))
          .eq("path", mapping.oldPath);

        if (updateError) {
          console.error(
            `❌ Error updating path ${mapping.oldPath}:`,
            updateError
          );
        } else {
          console.log(
            `✅ Path updated in Supabase: ${mapping.oldPath} -> ${mapping.newPath}`
          );
        }
      } catch (error) {
        console.error(
          `❌ Unexpected error updating path ${mapping.oldPath}:`,
          error
        );
      }
    }
  };

  const handleDeleteItem = async (nodeToDelete: TreeNode) => {
    console.log(
      `🗑️ Deleting ${nodeToDelete.name} (type: ${nodeToDelete.type})`
    );

    // Coletar todos os paths que serão deletados (incluindo filhos)
    const pathsToDelete: string[] = [];

    const collectPathsForDeletion = (node: TreeNode) => {
      // Se é uma página, adiciona o path
      if ((node.type === "page" || node.type === "subpage") && node.path) {
        pathsToDelete.push(node.path);
      }

      // Se é um grupo com página principal
      if (node.type === "group" && node.data?.page) {
        pathsToDelete.push(node.data.page);
      }

      // Recursivamente coletar paths dos filhos
      if (node.children) {
        node.children.forEach((child) => collectPathsForDeletion(child));
      }
    };

    collectPathsForDeletion(nodeToDelete);

    if (pathsToDelete.length > 0) {
      console.log(
        `🗑️ Deleting ${pathsToDelete.length} pages from database:`,
        pathsToDelete
      );

      // Deletar todas as páginas do banco de dados
      for (const path of pathsToDelete) {
        try {
          const { error } = await supabase
            .from("project_pages")
            .delete()
            .eq("project_id", parseInt(projectId, 10))
            .eq("path", path);

          if (error) {
            console.error(`❌ Error deleting page ${path}:`, error);
          } else {
            console.log(`✅ Deleted page from database: ${path}`);
          }
        } catch (error) {
          console.error(`❌ Unexpected error deleting page ${path}:`, error);
        }
      }
    }

    // Remover da árvore e salvar config
    const newTree = removeNodeById(tree, nodeToDelete.id);
    setTree(newTree);

    await saveConfigToSupabase(newTree);
  };

  const updatePageInSupabase = async (
    oldPath: string,
    newTitle: string,
    newPath?: string
  ) => {
    try {
      console.log(
        `💾 Updating page in Supabase: ${oldPath} -> ${newPath || "title only"}`
      );

      const updateData: { title: string; path?: string } = { title: newTitle };
      if (newPath && newPath !== oldPath) {
        updateData.path = newPath;
      }

      const { error: updateError } = await supabase
        .from("project_pages")
        .update(updateData)
        .eq("project_id", parseInt(projectId, 10))
        .eq("path", oldPath);

      if (updateError) {
        console.error("❌ Error updating page in Supabase:", updateError);
      } else {
        console.log("✅ Page updated in Supabase");
      }
    } catch (error) {
      console.error("❌ Unexpected error updating page:", error);
    }
  };

  // Processa todos os nós e corrige transformações para navbar items
  const processNodesForNavbarTransformation = (
    nodes: TreeNode[]
  ): TreeNode[] => {
    return nodes.map((node) => {
      // Se é um navbar-dropdown-item mas não está dentro de um dropdown, transformar em navbar-item
      if (
        node.type === "navbar" &&
        node.data?.type === "navbar-dropdown-item"
      ) {
        // Verificar se o pai é um dropdown
        const parent = findNodeById(tree, node.parentId || "");
        if (!parent || parent.data?.type !== "navbar-dropdown") {
          console.log(
            `🔄 Transforming orphaned navbar-dropdown-item "${node.name}" back to navbar-item`
          );
          return {
            ...node,
            data: {
              ...node.data,
              type: "navbar-item",
              dropdownIndex: undefined,
            },
            children: node.children
              ? processNodesForNavbarTransformation(node.children)
              : node.children,
          };
        }
      }

      // Processar filhos recursivamente
      return {
        ...node,
        children: node.children
          ? processNodesForNavbarTransformation(node.children)
          : node.children,
      };
    });
  };

  return (
    <div className="flex h-full ">
      <div className="w-80 h-full flex flex-col border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 h-7">
            {isProjectLoading ? (
              <Skeleton className="h-6 w-3/4" />
            ) : (
              selectedProject?.project_name || "Project Pages"
            )}
          </h2>
          <div className="flex items-center justify-between mt-2">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Drag and drop to reorganize
            </p>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">{renderSidebarContent()}</div>
      </div>

      {/* <ConfigJsonPreview config={liveConfig} /> */}

      {/* Add API Upload Modal */}
      <ApiUploadModal
        isOpen={isApiUploadOpen}
        onClose={() => setIsApiUploadOpen(false)}
        projectId={projectId}
        onSuccess={() => {
          // Force refresh the project data from database after successful API import
          forceRefreshProject();
        }}
        toast={{
          success: (msg) => addToast(msg, "success", "API Import"),
          error: (msg) => addToast(msg, "error", "API Import"),
        }}
      />
    </div>
  );
}
