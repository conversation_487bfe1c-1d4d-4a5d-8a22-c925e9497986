import { Node } from "@tiptap/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNodeViewRenderer } from "@tiptap/react";
import React, { useState, useCallback } from "react";
import { Edit3, Save, X, Play } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { NodeViewProps } from "@tiptap/react";
import { useToast } from "@/components/ToastProvider";

// Interface for video attributes
interface VideoAttrs {
  src: string;
  width?: string;
  alt?: string;
}

// React component for the video
const VideoComponent: React.FC<NodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const { addToast } = useToast();

  // Form data state
  const [formData, setFormData] = useState<VideoAttrs>({
    src: node.attrs.src || "",
    width: node.attrs.width || "500px",
    alt: node.attrs.alt || "",
  });

  // Update formData when node attrs change
  React.useEffect(() => {
    setFormData({
      src: node.attrs.src || "",
      width: node.attrs.width || "500px",
      alt: node.attrs.alt || "",
    });
  }, [node.attrs.src, node.attrs.width, node.attrs.alt]);

  const handleSave = useCallback(() => {
    // Validate required fields
    if (!formData.src.trim()) {
      addToast("Video source is required", "warning");
      return;
    }

    updateAttributes(formData);
    setIsEditing(false);
    addToast("Video updated successfully", "success", "Success");
  }, [formData, updateAttributes, addToast]);

  const handleCancel = useCallback(() => {
    setFormData({
      src: node.attrs.src || "",
      width: node.attrs.width || "500px",
      alt: node.attrs.alt || "",
    });
    setIsEditing(false);
  }, [node.attrs]);

  const handleInputChange = useCallback(
    (field: keyof VideoAttrs, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  // Function to convert YouTube URL to embed format
  const getEmbedUrl = (url: string) => {
    if (!url) return "";

    // If it's already an embed URL, return as is
    if (url.includes("embed")) return url;

    // Convert YouTube watch URL to embed URL
    const youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
    const match = url.match(youtubeRegex);

    if (match) {
      return `https://www.youtube.com/embed/${match[1]}`;
    }

    return url;
  };

  console.log("VideoNode - node.attrs:", {
    width: node.attrs.width,
    src: node.attrs.src,
    alt: node.attrs.alt,
    allAttrs: node.attrs,
  });

  if (!isEditing) {
    return (
      <NodeViewWrapper className="video-node" as="div" contentEditable={false}>
        <div
          className={`
          my-4 relative group block max-w-full mx-auto text-center
          ${selected ? "ring-2 ring-blue-400/50 rounded-lg" : ""}
        `}
        >
          {/* Edit button - only visible on hover */}
          {selected && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="absolute top-2 right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700 z-10 "
            >
              <Edit3 className="w-3 h-3 mr-1" />
              Edit
            </Button>
          )}

          {/* Video Display */}
          {node.attrs.src ? (
            <div
              style={{
                width: node.attrs.width || "500px",
                maxWidth: "100%",
                margin: "0 auto",
              }}
            >
              <iframe
                src={getEmbedUrl(node.attrs.src)}
                width="100%"
                height="315"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                title={node.attrs.alt || "Video"}
                className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
                style={{
                  aspectRatio: "16/9",
                }}
              />
            </div>
          ) : (
            <div
              style={{
                width: node.attrs.width || "500px",
                maxWidth: "100%",
                minHeight: "200px",
              }}
              className="flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg p-8 mx-auto"
            >
              <div className="text-center">
                <Play className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 dark:text-gray-400">
                  No video selected
                </p>
              </div>
            </div>
          )}
        </div>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper
      className="video-node"
      as="div"
      data-drag-handle=""
      contentEditable={false}
    >
      <div
        className={`my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg `}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4 ">
          <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            Edit Video
          </h4>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
            >
              <X className="w-3 h-3 mr-1" />
              Cancel
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleSave}
              className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="w-3 h-3 mr-1" />
              Save
            </Button>
          </div>
        </div>

        {/* Edit Form */}
        <div className="space-y-4">
          {/* Video Source */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Video Source *
            </label>
            <input
              type="url"
              value={formData.src}
              onChange={(e) => handleInputChange("src", e.target.value)}
              placeholder="https://www.youtube.com/embed/dQw4w9WgXcQ or https://www.youtube.com/watch?v=dQw4w9WgXcQ"
              className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Enter YouTube URL (watch or embed format)
            </p>
          </div>

          {/* Width */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Width
            </label>
            <input
              type="text"
              value={formData.width || "500px"}
              onChange={(e) => handleInputChange("width", e.target.value)}
              placeholder="500px, 100%, 80%"
              className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Enter width in pixels (e.g., 500px) or percentage (e.g., 80%)
            </p>
          </div>

          {/* Alt Text */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Alt Text
            </label>
            <input
              type="text"
              value={formData.alt || ""}
              onChange={(e) => handleInputChange("alt", e.target.value)}
              placeholder="Describe the video for accessibility"
              className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Alternative text helps with accessibility and SEO
            </p>
          </div>

          {/* Preview */}
          {formData.src && (
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Preview
              </label>
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-slate-900">
                <div
                  style={{
                    width: formData.width || "500px",
                    maxWidth: "100%",
                    margin: "0 auto",
                  }}
                >
                  <iframe
                    src={getEmbedUrl(formData.src)}
                    width="100%"
                    height="200"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    title={formData.alt || "Video Preview"}
                    className="rounded-lg shadow-md mx-auto"
                    style={{
                      aspectRatio: "16/9",
                    }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </NodeViewWrapper>
  );
};

// Tiptap extension definition
export const VideoNode = Node.create({
  name: "videoNode",
  group: "block",
  atom: true,
  draggable: false,
  selectable: true,

  addAttributes() {
    return {
      src: {
        default: "",
        parseHTML: (element) => element.getAttribute("src"),
        renderHTML: (attributes) => {
          if (!attributes.src) return {};
          return { src: attributes.src };
        },
      },
      width: {
        default: "500px",
        parseHTML: (element) => {
          const width =
            element.getAttribute("width") ||
            element.getAttribute("data-width") ||
            "500px";

          return width;
        },
        renderHTML: (attributes) => {
          if (!attributes.width) return {};
          return { width: attributes.width };
        },
      },
      alt: {
        default: "",
        parseHTML: (element) => element.getAttribute("alt"),
        renderHTML: (attributes) => {
          if (!attributes.alt) return {};
          return { alt: attributes.alt };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "Video",
        getAttrs: (element) => {
          if (!(element instanceof HTMLElement)) return false;
          const attrs = {
            src: element.getAttribute("src") || "",
            width: element.getAttribute("width") || "500px",
            alt: element.getAttribute("alt") || "",
          };
          return attrs;
        },
      },
      {
        tag: 'iframe[data-type="video"]',
        getAttrs: (element) => {
          if (!(element instanceof HTMLElement)) return false;
          const attrs = {
            src: element.getAttribute("src") || "",
            width: element.getAttribute("data-width") || "500px",
            alt: element.getAttribute("title") || "",
          };

          return attrs;
        },
      },
      {
        tag: 'div[data-type="video"]',
        getAttrs: (element) => {
          if (!(element instanceof HTMLElement)) return false;

          // Find the iframe child element
          const iframe = element.querySelector("iframe");
          if (!iframe) return false;

          const attrs = {
            src: iframe.getAttribute("src") || "",
            width: element.getAttribute("data-width") || "500px",
            alt: iframe.getAttribute("title") || "",
          };

          return attrs;
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    // Ensure width is preserved
    const width = HTMLAttributes.width || "500px";

    return [
      "div",
      {
        "data-type": "video",
        "data-width": width,
        class: "video-node",
      },
      [
        "iframe",
        {
          src: HTMLAttributes.src,
          width: "100%",
          height: "315",
          frameborder: "0",
          allow:
            "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
          allowfullscreen: "true",
          title: HTMLAttributes.alt,
          style: `width: ${width}; max-width: 100%; aspect-ratio: 16/9;`,
        },
      ],
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(VideoComponent);
  },

  addCommands() {
    return {
      setVideo:
        (attributes?: Partial<VideoAttrs>) =>
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ({ commands }: { commands: any }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes,
          });
        },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;
  },
});

export default VideoNode;
