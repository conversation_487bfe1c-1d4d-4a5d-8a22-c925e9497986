import { Node } from "@tiptap/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>per, ReactNodeViewRenderer } from "@tiptap/react";
import React, { useState, useCallback } from "react";
import { Edit3, Save, X, ImageIcon, Search, Sun, Moon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import type { NodeViewProps } from "@tiptap/react";
import { ImageSelector } from "../ImageSelector";
import { useProject } from "@/contexts";
import { useToast } from "@/components/ToastProvider";

// Interface for image attributes
interface ImageAttrs {
  src: string;
  srcDark?: string;
  size?: string;
  alt?: string;
}

// React component for the image
const ImageComponent: React.FC<NodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showImageSelector, setShowImageSelector] = useState(false);
  const [selectingForDarkMode, setSelectingForDarkMode] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const { selectedProject } = useProject();
  const { addToast } = useToast();

  // Form data state
  const [formData, setFormData] = useState<ImageAttrs>({
    src: node.attrs.src || "",
    srcDark: node.attrs.srcDark || "",
    size: node.attrs.size || "20px",
    alt: node.attrs.alt || "",
  });

  // Update formData when node attrs change
  React.useEffect(() => {
    setFormData({
      src: node.attrs.src || "",
      srcDark: node.attrs.srcDark || "",
      size: node.attrs.size || "20%",
      alt: node.attrs.alt || "",
    });
  }, [node.attrs.src, node.attrs.srcDark, node.attrs.size, node.attrs.alt]);

  // Check if dark mode is active
  React.useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains("dark"));
    };

    checkDarkMode();
    // Watch for dark mode changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    });

    return () => observer.disconnect();
  }, []);

  const handleSave = useCallback(() => {
    // Validate required fields
    if (!formData.src.trim()) {
      addToast("Image source is required", "warning");
      return;
    }

    updateAttributes(formData);
    setIsEditing(false);
    addToast("Image updated successfully", "success", "Success");
  }, [formData, updateAttributes, addToast]);

  const handleCancel = useCallback(() => {
    setFormData({
      src: node.attrs.src || "",
      srcDark: node.attrs.srcDark || "",
      size: node.attrs.size || "20px",
      alt: node.attrs.alt || "",
    });
    setIsEditing(false);
  }, [node.attrs]);

  const handleInputChange = useCallback(
    (field: keyof ImageAttrs, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  const handleImageSelect = useCallback(
    (imageUrl: string) => {
      if (selectingForDarkMode) {
        handleInputChange("srcDark", imageUrl);
        setSelectingForDarkMode(false);
      } else {
        handleInputChange("src", imageUrl);
      }
      setShowImageSelector(false);
    },
    [handleInputChange, selectingForDarkMode]
  );

  const handleOpenImageSelector = useCallback(
    (forDarkMode: boolean = false) => {
      setSelectingForDarkMode(forDarkMode);
      setShowImageSelector(true);
    },
    []
  );
  // Determine which image to show based on dark mode
  const currentImageSrc =
    isDarkMode && (isEditing ? formData.srcDark : node.attrs.srcDark)
      ? isEditing
        ? formData.srcDark
        : node.attrs.srcDark
      : isEditing
      ? formData.src
      : node.attrs.src;

  console.log("ImageNode - node.attrs:", {
    size: node.attrs.size,
    src: node.attrs.src,
    srcDark: node.attrs.srcDark,
    alt: node.attrs.alt,
    allAttrs: node.attrs,
  });

  if (!isEditing) {
    return (
      <NodeViewWrapper className="image-node" as="div" contentEditable={false}>
        <div
          className={`
          my-4 relative group block max-w-full mx-auto text-center
          ${selected ? "ring-2 ring-blue-400/50 rounded-lg" : ""}
        `}
        >
          {/* Edit button - only visible on hover */}
          {selected && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="absolute top-2 right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700 z-10 "
            >
              <Edit3 className="w-3 h-3 mr-1" />
              Edit
            </Button>
          )}

          {/* Dark mode indicator */}
          {node.attrs.srcDark && (
            <div className="absolute top-2 left-2 flex items-center space-x-1 bg-black/50 text-white px-2 py-1 rounded text-xs">
              {isDarkMode ? (
                <>
                  <Moon className="w-3 h-3" />
                  <span>Dark</span>
                </>
              ) : (
                <>
                  <Sun className="w-3 h-3" />
                  <span>Light</span>
                </>
              )}
            </div>
          )}

          {/* Image Display */}
          {currentImageSrc ? (
            <img
              src={currentImageSrc}
              alt={node.attrs.alt || ""}
              width={parseInt(node.attrs.size?.replace("px", "") || "200")}
              height={200}
              style={{
                width: node.attrs.size || "20px",
                maxWidth: node.attrs.size,
                height: "auto",
              }}
              className="rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
              onError={(e) => {
                e.currentTarget.src =
                  "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23f3f4f6'/%3E%3Ctext x='200' y='150' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='20'%3EImage not found%3C/text%3E%3C/svg%3E";
              }}
            />
          ) : (
            <div
              style={{
                width: node.attrs.size || "20%",
                maxWidth: "100%",
                minHeight: "200px",
              }}
              className="flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg p-8 mx-auto"
            >
              <div className="text-center">
                <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 dark:text-gray-400">
                  No image selected
                </p>
              </div>
            </div>
          )}
        </div>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper
      className="image-node"
      as="div"
      data-drag-handle=""
      contentEditable={false}
    >
      <div
        className={`my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg `}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4 ">
          <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            Edit Image
          </h4>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
            >
              <X className="w-3 h-3 mr-1" />
              Cancel
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleSave}
              className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="w-3 h-3 mr-1" />
              Save
            </Button>
          </div>
        </div>

        {/* Edit Form */}
        <div className="space-y-4">
          {/* Image Source */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Image Source *
            </label>
            <div className="flex space-x-2">
              <input
                type="url"
                value={formData.src}
                onChange={(e) => handleInputChange("src", e.target.value)}
                placeholder="https://example.com/image.jpg"
                className="flex-1 px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleOpenImageSelector(false)}
                className="flex items-center space-x-1"
              >
                <Search className="w-3 h-3" />
                <span>Browse</span>
              </Button>
            </div>
          </div>

          {/* Dark Mode Image Source */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Dark Mode Image (Optional)
            </label>
            <div className="flex space-x-2">
              <input
                type="url"
                value={formData.srcDark || ""}
                onChange={(e) => handleInputChange("srcDark", e.target.value)}
                placeholder="https://example.com/image-dark.jpg"
                className="flex-1 px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleOpenImageSelector(true)}
                className="flex items-center space-x-1"
              >
                <Search className="w-3 h-3" />
                <span>Browse</span>
              </Button>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              This image will be shown when dark mode is active
            </p>
          </div>

          {/* Size */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Size
            </label>
            <input
              type="text"
              value={formData.size || "100%"}
              onChange={(e) => handleInputChange("size", e.target.value)}
              placeholder="100%, 500px, 80%"
              className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Enter size in pixels (e.g., 500px) or percentage (e.g., 80%)
            </p>
          </div>

          {/* Alt Text */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Alt Text
            </label>
            <input
              type="text"
              value={formData.alt || ""}
              onChange={(e) => handleInputChange("alt", e.target.value)}
              placeholder="Describe the image for accessibility"
              className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Alternative text helps with accessibility and SEO
            </p>
          </div>

          {/* Preview */}
          {formData.src && (
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Preview
              </label>
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-slate-900">
                <img
                  src={
                    isDarkMode &&
                    (isEditing ? formData.srcDark : node.attrs.srcDark)
                      ? isEditing
                        ? formData.srcDark
                        : node.attrs.srcDark
                      : isEditing
                      ? formData.src
                      : node.attrs.src
                  }
                  alt={formData.alt || "Preview"}
                  width={400}
                  height={300}
                  style={{
                    width: formData.size || "100%",
                    maxWidth: "100%",
                    height: "auto",
                  }}
                  className="rounded-lg shadow-md mx-auto"
                  onError={(e) => {
                    e.currentTarget.src =
                      "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23f3f4f6'/%3E%3Ctext x='200' y='150' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='20'%3EImage not found%3C/text%3E%3C/svg%3E";
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Image Selector Modal */}
      <ImageSelector
        isOpen={showImageSelector}
        onClose={() => setShowImageSelector(false)}
        onSelectImage={handleImageSelect}
        projectId={selectedProject?.id}
        organizationId={selectedProject?.owner_id}
      />
    </NodeViewWrapper>
  );
};

// Tiptap extension definition
export const ImageNode = Node.create({
  name: "imageNode",
  group: "block",
  atom: true,
  draggable: false,
  selectable: true,

  addAttributes() {
    return {
      src: {
        default: "",
        parseHTML: (element) => element.getAttribute("src"),
        renderHTML: (attributes) => {
          if (!attributes.src) return {};
          return { src: attributes.src };
        },
      },
      srcDark: {
        default: "",
        parseHTML: (element) =>
          element.getAttribute("srcDark") || element.getAttribute("src-dark"),
        renderHTML: (attributes) => {
          if (!attributes.srcDark) return {};
          return { srcDark: attributes.srcDark };
        },
      },
      size: {
        default: "50%",
        parseHTML: (element) => {
          const size =
            element.getAttribute("size") ||
            element.getAttribute("data-size") ||
            "50%";

          return size;
        },
        renderHTML: (attributes) => {
          if (!attributes.size) return {};
          return { size: attributes.size };
        },
      },
      alt: {
        default: "",
        parseHTML: (element) => element.getAttribute("alt"),
        renderHTML: (attributes) => {
          if (!attributes.alt) return {};
          return { alt: attributes.alt };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "Image",
        getAttrs: (element) => {
          if (!(element instanceof HTMLElement)) return false;
          const attrs = {
            src: element.getAttribute("src") || "",
            srcDark: element.getAttribute("srcDark") || "",
            size: element.getAttribute("size") || "50%",
            alt: element.getAttribute("alt") || "",
          };
          return attrs;
        },
      },
      {
        tag: 'img[data-type="media"]',
        getAttrs: (element) => {
          if (!(element instanceof HTMLElement)) return false;
          const attrs = {
            src: element.getAttribute("src") || "",
            srcDark: element.getAttribute("data-src-dark") || "",
            size: "50%",
            alt: element.getAttribute("alt") || "",
          };

          return attrs;
        },
      },
      {
        tag: 'div[data-type="image"]',
        getAttrs: (element) => {
          if (!(element instanceof HTMLElement)) return false;

          // Find the img child element
          const img = element.querySelector("img");
          if (!img) return false;

          const attrs = {
            src: img.getAttribute("src") || "",
            srcDark: img.getAttribute("data-src-dark") || "",
            size: element.getAttribute("data-size") || "50%",
            alt: img.getAttribute("alt") || "",
          };

          return attrs;
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    // Ensure size is preserved
    const size = HTMLAttributes.size || "100%";

    return [
      "div",
      {
        "data-type": "image",
        "data-size": size,
        class: "image-node",
      },
      [
        "img",
        {
          src: HTMLAttributes.src,
          "data-src-dark": HTMLAttributes.srcDark,
          alt: HTMLAttributes.alt,
          style: `width: ${size}; max-width: 100%;`,
        },
      ],
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(ImageComponent);
  },

  addCommands() {
    return {
      setImage:
        (attributes?: Partial<ImageAttrs>) =>
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ({ commands }: { commands: any }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes,
          });
        },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;
  },
});

export default ImageNode;
