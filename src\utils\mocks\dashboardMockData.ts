import { getMockProjectDisplayName } from "./mockProjects";

interface AnalyticsData {
	dashboard?: {
		timeseries?: Array<unknown>;
		totals?: {
			requests?: number;
			bandwidth?: number;
			uniques?: number;
			pageViews?: number;
			threats?: number;
			bounceRate?: number;
			avgPageLoadTime?: number;
		};
	};
	firewall?: Array<unknown>;
	zone?: unknown;
	zoneId?: string;
	zoneName?: string;
	siteTag?: string;
	siteName?: string;
	projectName?: string;
	timeRange?: {
		since: string;
		until: string;
	};
	apiVersion?: string;
	debug?: {
		availableSites?: WebAnalyticsSite[];
		searchedKeywords?: string[];
		message?: string;
		suggestion?: string;
	};
}

interface WebAnalyticsSite {
	site_tag: string;
	host: string;
}

interface WebAnalyticsTopNsResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	data: {
		countries: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topReferers: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topPaths: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topBrowsers: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topOSs: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topDeviceTypes: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
	};
}

interface WebAnalyticsSparklineResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	data: {
		visits?: Array<{
			sum: { visits: number };
			dimensions: { ts: string };
		}>;
		pageviews?: Array<{
			count: number;
			dimensions: { ts: string };
		}>;
	};
}

interface WebAnalyticsTimeseriesResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	groupBy?: string;
	data: {
		series?: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { ts: string; metric: string };
		}>;
	};
}

/**
 * Generate mock analytics data for dashboard
 */
export const generateMockAnalyticsData = (
	projectName: string,
	timeRange: { since: string; until: string; label: string }
): AnalyticsData => {
	const displayName = getMockProjectDisplayName(projectName);

	// Calculate days from time range
	let days = 7;
	if (timeRange.since === "-30d") days = 30;
	else if (timeRange.since === "-90d") days = 90;

	// Generate realistic totals based on project type
	const baseVisits = days === 7 ? 2500 : days === 30 ? 12000 : 35000;
	const variation = Math.floor(baseVisits * 0.2); // 20% variation
	const totalVisits =
		baseVisits + Math.floor(Math.random() * variation) - variation / 2;
	const totalPageViews = Math.floor(totalVisits * 1.4); // 1.4 pages per visit

	return {
		dashboard: {
			totals: {
				requests: totalVisits,
				pageViews: totalPageViews,
				uniques: Math.floor(totalVisits * 0.85), // 85% unique visitors
				bounceRate: 45 + Math.floor(Math.random() * 20), // 45-65%
				avgPageLoadTime: 1200 + Math.floor(Math.random() * 800), // 1.2-2.0s
				bandwidth: Math.floor(totalPageViews * 2.5 * 1024 * 1024), // ~2.5MB per page
				threats: Math.floor(totalVisits * 0.001), // 0.1% threats
			},
		},
		siteTag: `mock_${projectName}_tag`,
		siteName: `${displayName} Documentation`,
		projectName: displayName,
		timeRange,
		apiVersion: "Mock Web Analytics API v1.0",
		debug: {
			message: `Mock data generated for ${displayName} 🎭`,
			suggestion: `This is demonstration data for the ${timeRange.label.toLowerCase()} period`,
			availableSites: [
				{
					site_tag: `mock_${projectName}_tag`,
					host: `docs.${projectName}.com.br`,
				},
			],
		},
	};
};

/**
 * Generate mock TopNs data for dashboard
 */
export const generateMockTopNsData = (
	projectName: string,
	timeRange: { since: string; until: string }
): WebAnalyticsTopNsResponse => {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const displayName = getMockProjectDisplayName(projectName);

	return {
		success: true,
		siteTag: `mock_${projectName}_tag`,
		dateRange: timeRange,
		data: {
			countries: [
				{ count: 1, sum: { visits: 8500 }, dimensions: { metric: "Brazil" } },
				{
					count: 1,
					sum: { visits: 1200 },
					dimensions: { metric: "United States" },
				},
				{ count: 1, sum: { visits: 800 }, dimensions: { metric: "Argentina" } },
				{ count: 1, sum: { visits: 600 }, dimensions: { metric: "Mexico" } },
				{ count: 1, sum: { visits: 400 }, dimensions: { metric: "Chile" } },
			],
			topReferers: [
				{
					count: 1,
					sum: { visits: 3200 },
					dimensions: { metric: "google.com" },
				},
				{
					count: 1,
					sum: { visits: 1800 },
					dimensions: { metric: `${projectName}.com.br` },
				},
				{
					count: 1,
					sum: { visits: 1200 },
					dimensions: { metric: "github.com" },
				},
				{
					count: 1,
					sum: { visits: 800 },
					dimensions: { metric: "stackoverflow.com" },
				},
				{
					count: 1,
					sum: { visits: 600 },
					dimensions: { metric: "medium.com" },
				},
			],
			topPaths: [
				{
					count: 1,
					sum: { visits: 2500 },
					dimensions: { metric: "/api-reference" },
				},
				{
					count: 1,
					sum: { visits: 2000 },
					dimensions: { metric: "/getting-started" },
				},
				{
					count: 1,
					sum: { visits: 1800 },
					dimensions: { metric: "/integration-guide" },
				},
				{
					count: 1,
					sum: { visits: 1200 },
					dimensions: { metric: "/webhooks" },
				},
				{
					count: 1,
					sum: { visits: 1000 },
					dimensions: { metric: "/payment-methods" },
				},
				{ count: 1, sum: { visits: 800 }, dimensions: { metric: "/sandbox" } },
			],
			topBrowsers: [
				{ count: 1, sum: { visits: 4200 }, dimensions: { metric: "Chrome" } },
				{ count: 1, sum: { visits: 2800 }, dimensions: { metric: "Safari" } },
				{ count: 1, sum: { visits: 1800 }, dimensions: { metric: "Firefox" } },
				{ count: 1, sum: { visits: 1200 }, dimensions: { metric: "Edge" } },
				{ count: 1, sum: { visits: 500 }, dimensions: { metric: "Opera" } },
			],
			topOSs: [
				{ count: 1, sum: { visits: 4500 }, dimensions: { metric: "Windows" } },
				{ count: 1, sum: { visits: 3200 }, dimensions: { metric: "macOS" } },
				{ count: 1, sum: { visits: 2100 }, dimensions: { metric: "Android" } },
				{ count: 1, sum: { visits: 1800 }, dimensions: { metric: "iOS" } },
				{ count: 1, sum: { visits: 900 }, dimensions: { metric: "Linux" } },
			],
			topDeviceTypes: [
				{ count: 1, sum: { visits: 6800 }, dimensions: { metric: "desktop" } },
				{ count: 1, sum: { visits: 3900 }, dimensions: { metric: "mobile" } },
				{ count: 1, sum: { visits: 800 }, dimensions: { metric: "tablet" } },
			],
		},
	};
};

/**
 * Generate mock sparkline data for dashboard
 */
export const generateMockSparklineData = (
	projectName: string,
	timeRange: { since: string; until: string }
): WebAnalyticsSparklineResponse => {
	// Calculate days from time range
	let days = 7;
	if (timeRange.since === "-30d") days = 30;
	else if (timeRange.since === "-90d") days = 90;

	const visits: Array<{ sum: { visits: number }; dimensions: { ts: string } }> =
		[];
	const pageviews: Array<{ count: number; dimensions: { ts: string } }> = [];

	// Generate daily data
	for (let i = days - 1; i >= 0; i--) {
		const date = new Date();
		date.setDate(date.getDate() - i);
		const timestamp = date.toISOString();

		// Generate realistic daily visits (weekends typically lower)
		const dayOfWeek = date.getDay();
		const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
		const baseVisits = isWeekend ? 180 : 280;
		const dailyVisits = baseVisits + Math.floor(Math.random() * 100) - 50;
		const dailyPageviews = Math.floor(dailyVisits * 1.4);

		visits.push({
			sum: { visits: dailyVisits },
			dimensions: { ts: timestamp },
		});

		pageviews.push({
			count: dailyPageviews,
			dimensions: { ts: timestamp },
		});
	}

	return {
		success: true,
		siteTag: `mock_${projectName}_tag`,
		dateRange: timeRange,
		data: {
			visits,
			pageviews,
		},
	};
};

/**
 * Generate mock timeseries data for dashboard
 */
export const generateMockTimeseriesData = (
	projectName: string,
	timeRange: { since: string; until: string },
	groupBy: string
): WebAnalyticsTimeseriesResponse => {
	// Calculate days from time range
	let days = 7;
	if (timeRange.since === "-30d") days = 30;
	else if (timeRange.since === "-90d") days = 90;

	const series: Array<{
		count: number;
		sum: { visits: number };
		dimensions: { ts: string; metric: string };
	}> = [];

	// Define metrics based on groupBy parameter
	let metrics: string[] = [];
	switch (groupBy) {
		case "countryName":
			metrics = ["Brazil", "United States", "Argentina"];
			break;
		case "refererHost":
			metrics = ["google.com", `${projectName}.com.br`, "github.com"];
			break;
		case "requestPath":
			metrics = ["/api-reference", "/getting-started", "/integration-guide"];
			break;
		case "userAgentBrowser":
			metrics = ["Chrome", "Safari", "Firefox"];
			break;
		case "userAgentOS":
			metrics = ["Windows", "macOS", "Android"];
			break;
		case "deviceType":
			metrics = ["desktop", "mobile", "tablet"];
			break;
		default:
			metrics = ["unknown"];
	}

	// Generate timeseries data for each metric
	for (let i = days - 1; i >= 0; i--) {
		const date = new Date();
		date.setDate(date.getDate() - i);
		const timestamp = date.toISOString();

		metrics.forEach((metric, index) => {
			const baseVisits = (metrics.length - index) * 50; // Decreasing visits for each metric
			const dailyVisits = baseVisits + Math.floor(Math.random() * 30) - 15;

			series.push({
				count: 1,
				sum: { visits: Math.max(0, dailyVisits) },
				dimensions: { ts: timestamp, metric },
			});
		});
	}

	return {
		success: true,
		siteTag: `mock_${projectName}_tag`,
		dateRange: timeRange,
		groupBy,
		data: { series },
	};
};
