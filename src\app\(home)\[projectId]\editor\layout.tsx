"use client";

import ProjectPagesSidebar from "@/components/editor/ProjectPagesSidebar";
import { useParams } from "next/navigation";

export default function EditorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();
  const projectId = Array.isArray(params.projectId)
    ? params.projectId[0]
    : params.projectId;

  if (!projectId) {
    return <p>Loading project information...</p>;
  }

  return (
    <div className="flex h-full">
      <div className="flex">
        <ProjectPagesSidebar projectId={projectId} />
      </div>
      <main className="flex-1 h-full overflow-y-auto">
        {children} {/* This will render the content from page.tsx files */}
      </main>

      {/* Editor Tour */}
    </div>
  );
}
