import {
	BotStatistics,
	BotQuestion,
	DocsBotQuestionsResponse,
	DocsBotPagination,
} from "@/app/(dashboard)/[projectId]/ask-ai/types";
import { getMockProjectDisplayName } from "./mockProjects";

/**
 * Generate mock statistics data for Ask AI
 */
export const generateMockAskAiStats = (
	projectName: string,
	timeDelta: string
): BotStatistics => {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const displayName = getMockProjectDisplayName(projectName);
	const days = parseInt(timeDelta);

	// Generate realistic data based on time period
	const baseQuestions = days === 7 ? 45 : days === 30 ? 180 : 520;
	const dailyVariation = Math.floor(baseQuestions / days);

	// Generate labels (dates)
	const labels: string[] = [];
	const countData: number[] = [];
	const negativeData: number[] = [];
	const positiveData: number[] = [];
	const couldAnswerData: number[] = [];
	const escalatedData: number[] = [];

	for (let i = days - 1; i >= 0; i--) {
		const date = new Date();
		date.setDate(date.getDate() - i);
		labels.push(date.toISOString().split("T")[0]);

		// Generate realistic daily counts with some variation
		const dailyCount = Math.max(
			0,
			dailyVariation + Math.floor(Math.random() * 10) - 5
		);
		const positive = Math.floor(dailyCount * (0.7 + Math.random() * 0.2)); // 70-90% positive
		const negative = Math.floor(dailyCount * 0.1); // ~10% negative
		const couldAnswer = Math.floor(dailyCount * (0.8 + Math.random() * 0.15)); // 80-95% could answer
		const escalated = Math.floor(dailyCount * (0.05 + Math.random() * 0.05)); // 5-10% escalated

		countData.push(dailyCount);
		positiveData.push(positive);
		negativeData.push(negative);
		couldAnswerData.push(couldAnswer);
		escalatedData.push(escalated);
	}

	const totalQuestions = countData.reduce((sum, count) => sum + count, 0);
	const totalPositive = positiveData.reduce((sum, count) => sum + count, 0);
	const totalCouldAnswer = couldAnswerData.reduce(
		(sum, count) => sum + count,
		0
	);
	const totalEscalated = escalatedData.reduce((sum, count) => sum + count, 0);

	return {
		totalCount: totalQuestions,
		resolutionRate: `${Math.round(
			(totalPositive / Math.max(totalQuestions, 1)) * 100
		)}`,
		couldAnswerRate: `${Math.round(
			(totalCouldAnswer / Math.max(totalQuestions, 1)) * 100
		)}`,
		deflectionRate: `${Math.round(
			((totalQuestions - totalEscalated) / Math.max(totalQuestions, 1)) * 100
		)}`,
		timeSaved: Math.round(totalQuestions * 5.5), // ~5.5 minutes saved per question
		isMockData: true,

		labels,
		countData,
		negativeData,
		positiveData,
		couldAnswerData,
		escalatedData,

		// Rating distribution
		percentageLabels: ["Positive", "Neutral", "Negative"],
		counts: [
			totalPositive,
			Math.max(
				0,
				totalQuestions -
					totalPositive -
					negativeData.reduce((sum, count) => sum + count, 0)
			),
			negativeData.reduce((sum, count) => sum + count, 0),
		],

		// Escalation data
		escalatedLabels: ["Resolved", "Escalated"],
		escalatedCounts: [totalQuestions - totalEscalated, totalEscalated],

		// Answer capability
		answerLabels: ["Could Answer", "Could Not Answer"],
		answerCounts: [totalCouldAnswer, totalQuestions - totalCouldAnswer],
	};
};

/**
 * Generate mock questions/logs data for Ask AI
 */
export const generateMockAskAiLogs = (
	projectName: string,
	page: number = 0,
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	_filters: Record<string, unknown> = {},
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	_dateRange: Record<string, unknown> = {}
): DocsBotQuestionsResponse => {
	const displayName = getMockProjectDisplayName(projectName);
	const perPage = 20;
	const totalCount = 156; // Total mock questions

	// Generate mock questions
	const questions: BotQuestion[] = [];
	const startIndex = page * perPage;
	const endIndex = Math.min(startIndex + perPage, totalCount);

	const sampleQuestions = [
		{
			question: `How to integrate ${displayName} payment API?`,
			answer: `To integrate the ${displayName} payment API, you need to follow these steps:\n\n1. First, obtain your API credentials from the developer panel\n2. Configure the sandbox environment for testing\n3. Implement authentication using your API keys\n4. Make calls to the payment endpoints\n\nYou can find code examples and complete documentation in our APIs section.`,
			rating: 1,
			couldAnswer: true,
			escalation: false,
		},
		{
			question: `What are ${displayName} transaction fees?`,
			answer: `${displayName} fees vary depending on the transaction type:\n\n• Debit card: 1.99%\n• Credit card (one-time): 2.99%\n• Credit card (installments): 3.99%\n• PIX: 0.99%\n• Bank slip: $2.50 per transaction\n\nFor higher volumes, we have special conditions. Contact our sales team.`,
			rating: 1,
			couldAnswer: true,
			escalation: false,
		},
		{
			question: `How to implement ${displayName} webhooks?`,
			answer: `To implement ${displayName} webhooks:\n\n1. Configure your webhook URL in the panel\n2. Implement an endpoint to receive notifications\n3. Validate notification signatures for security\n4. Process received events\n\nExample events: payment.approved, payment.cancelled, refund.completed`,
			rating: 1,
			couldAnswer: true,
			escalation: false,
		},
		{
			question: `Does ${displayName} accept international payments?`,
			answer: `Yes, ${displayName} accepts international payments through Visa and Mastercard credit cards. Transactions are processed in Brazilian Real (BRL) and currency conversion is done automatically.\n\nAdditional fees may apply for international transactions.`,
			rating: 1,
			couldAnswer: true,
			escalation: false,
		},
		{
			question: `How does payment splitting work in ${displayName}?`,
			answer: `Payment splitting allows you to divide a transaction between multiple recipients:\n\n1. Configure recipients in the transaction\n2. Define percentages or fixed amounts\n3. The system distributes automatically\n4. Each recipient receives in their account\n\nUseful for marketplaces and multi-vendor platforms.`,
			rating: 1,
			couldAnswer: true,
			escalation: false,
		},
		{
			question: `SSL certificate issues in integration`,
			answer: `To resolve SSL issues in integration:\n\n1. Verify you're using HTTPS\n2. Update root certificates\n3. Configure TLS 1.2 or higher\n4. Check certificate chain\n\nIf the problem persists, our technical team can help.`,
			rating: 0,
			couldAnswer: true,
			escalation: true,
		},
		{
			question: `How to cancel a transaction in ${displayName}?`,
			answer: `To cancel a transaction in ${displayName}:\n\n1. Access the admin panel\n2. Locate the transaction by ID\n3. Click "Cancel transaction"\n4. Confirm cancellation\n\nVia API, use the POST /transactions/{id}/cancel endpoint`,
			rating: 1,
			couldAnswer: true,
			escalation: false,
		},
		{
			question: `Does ${displayName} have a testing sandbox?`,
			answer: `Yes! ${displayName} offers a complete sandbox environment:\n\n• URL: https://sandbox.${projectName}.com.br\n• Test credentials available in the panel\n• All production features\n• Test card data provided\n\nWe recommend testing the entire integration before going to production.`,
			rating: 1,
			couldAnswer: true,
			escalation: false,
		},
	];

	for (let i = startIndex; i < endIndex; i++) {
		const sampleIndex = i % sampleQuestions.length;
		const sample = sampleQuestions[sampleIndex];
		const createdDate = new Date();
		createdDate.setDate(createdDate.getDate() - Math.floor(Math.random() * 30));

		questions.push({
			id: `mock_${i + 1}`,
			createdAt: createdDate.toISOString(),
			couldAnswer: sample.couldAnswer,
			alias: `${displayName} Assistant`,
			question: sample.question,
			answer: sample.answer,
			rating: sample.rating,
			sources: [
				{
					title: `${displayName} - API Documentation`,
					url: `https://docs.${projectName}.com.br/api`,
					sourceId: `doc_${i + 1}`,
					content: "Official payment API documentation",
					page: "api-reference",
					type: "documentation",
					used: true,
				},
				{
					title: `${displayName} - Integration Guide`,
					url: `https://docs.${projectName}.com.br/integration`,
					sourceId: `guide_${i + 1}`,
					content: "Step-by-step integration guide",
					page: "integration-guide",
					type: "guide",
					used: Math.random() > 0.5,
				},
			],
			escalation: sample.escalation,
			ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
			metadata: {
				referrer: `https://${projectName}.com.br`,
				userAgent:
					"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			},
			standaloneQuestion: sample.question,
			run_id: `run_${Date.now()}_${i}`,
			testing: false,
		});
	}

	const pagination: DocsBotPagination = {
		perPage,
		page,
		viewableCount: questions.length,
		totalCount,
		planLimit: "unlimited",
		hasMorePages: (page + 1) * perPage < totalCount,
	};

	return {
		questions,
		pagination,
	};
};
