import axios from "axios";
// child_process (exec, execAsync) is no longer needed as Wrangler CLI interaction is removed.
// import { exec } from "child_process";
// import { promisify } from "util";

const CLOUDFLARE_API_TOKEN = process.env.CLOUDFLARE_API_TOKEN as string;
const CLOUDFLARE_ACCOUNT_ID = process.env.CLOUDFLARE_ACCOUNT_ID as string;

// Read additional environment variables
const SKIP_DEPENDENCY_INSTALL_VALUE = process.env
	.SKIP_DEPENDENCY_INSTALL as string;
const ALGOLIA_ADMIN_KEY_VALUE = process.env
	.DOCUSAURUS_ALGOLIA_ADMIN_KEY as string;
const ALGOLIA_APP_ID_VALUE = process.env.DOCUSAURUS_ALGOLIA_APP_ID as string;

// const execAsync = promisify(exec); // No longer needed

// Updated interfaces for better type safety and clarity
interface PagesProjectBuildConfig {
	build_command?: string;
	destination_dir?: string;
	root_dir?: string;
	web_analytics_tag?: string;
	web_analytics_token?: string;
	build_caching?: boolean;
}

interface PagesProjectSourceConfig {
	owner?: string;
	repo_name?: string;
	production_branch?: string;
	deployments_enabled?: boolean;
	pr_comments_enabled?: boolean;
}

interface PagesProjectSource {
	type?: "github" | "gitlab" | "direct_upload";
	config?: PagesProjectSourceConfig;
}

// --- New interfaces for Deployment Configs and Environment Variables ---
interface PagesEnvVar {
	type?: "plain_text" | "secret_text";
	value?: string;
}

interface PagesEnvVarSettings {
	[key: string]: PagesEnvVar;
}

interface PagesDeploymentEnvConfig {
	env_vars?: PagesEnvVarSettings;
	// Potentially other settings like kv_namespaces, d1_databases, etc.
}

interface PagesProjectDeploymentConfigs {
	production?: PagesDeploymentEnvConfig;
	preview?: PagesDeploymentEnvConfig;
}

// New interfaces for Deployments
interface PagesDeployment {
	id: string;
	short_id: string;
	project_id: string;
	project_name: string;
	environment: "production" | "preview";
	url: string;
	created_on: string;
	modified_on: string;
	latest_stage: {
		name: string;
		started_on: string | null;
		ended_on: string | null;
		status: "idle" | "active" | "canceled" | "success" | "failure";
	};
	deployment_trigger: {
		type: string;
		metadata?: {
			branch?: string;
			commit_hash?: string;
			commit_message?: string;
		};
	};
	stages: Array<{
		name: string;
		started_on: string | null;
		ended_on: string | null;
		status: "idle" | "active" | "canceled" | "success" | "failure";
	}>;
	build_config: {
		build_command?: string;
		destination_dir?: string;
		root_dir?: string;
		web_analytics_tag?: string;
		web_analytics_token?: string;
	};
	source?: {
		type: string;
		config?: {
			owner?: string;
			repo_name?: string;
			production_branch?: string;
			pr_comments_enabled?: boolean;
			deployments_enabled?: boolean;
		};
	};
	env_vars?: PagesEnvVarSettings;
	kv_namespaces?: unknown;
	durable_object_namespaces?: unknown;
	queue_producers?: unknown;
	r2_buckets?: unknown;
	d1_databases?: unknown;
	vectorize_bindings?: unknown;
	hyperdrive_bindings?: unknown;
	service_bindings?: unknown;
	browser_bindings?: unknown;
	ai_bindings?: unknown;
}

// --- End of new interfaces ---

interface PagesProject {
	id?: string;
	name?: string;
	subdomain?: string;
	domains?: string[];
	source?: PagesProjectSource;
	build_config?: PagesProjectBuildConfig;
	deployment_configs?: PagesProjectDeploymentConfigs;
	created_on?: string;
	production_branch?: string; // Top-level production branch
	// Add other relevant fields like canonical_deployment, latest_deployment if needed
}

async function getProject(projectName: string): Promise<PagesProject> {
	const response = await axios.get(
		`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/pages/projects/${projectName}`,
		{
			headers: {
				Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
				"Content-Type": "application/json",
			},
		}
	);
	return response.data.result;
}

// Helper to construct environment variables object
const getEnvVarsPayload = (): PagesEnvVarSettings => ({
	SKIP_DEPENDENCY_INSTALL: {
		type: "plain_text",
		value: SKIP_DEPENDENCY_INSTALL_VALUE,
	},
	ALGOLIA_ADMIN_KEY: {
		type: "secret_text", // Sensitive key
		value: ALGOLIA_ADMIN_KEY_VALUE,
	},
	ALGOLIA_APP_ID: {
		type: "plain_text",
		value: ALGOLIA_APP_ID_VALUE,
	},
});

async function createProject(
	cloudflareProjectName: string,
	productionBranch: string,
	githubOwner: string,
	githubActualRepoName: string,
	buildCommand: string,
	destinationDir: string,
	rootDir: string
): Promise<PagesProject> {
	const envVarsPayload = getEnvVarsPayload();
	console.log("envVarsPayload", envVarsPayload);
	const payload = {
		name: cloudflareProjectName,
		production_branch: productionBranch,
		source: {
			type: "github" as const,
			config: {
				owner: githubOwner,
				repo_name: githubActualRepoName,
				production_branch: productionBranch,
				deployments_enabled: true,
				pr_comments_enabled: true,
			},
		},
		build_config: {
			build_command: buildCommand,
			destination_dir: destinationDir,
			root_dir: rootDir,
			build_caching: true,
		},
		deployment_configs: {
			production: {
				env_vars: envVarsPayload,
			},
			preview: {
				env_vars: envVarsPayload,
			},
		},
	};
	const response = await axios.post(
		`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/pages/projects`,
		payload,
		{
			headers: {
				Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
				"Content-Type": "application/json",
			},
		}
	);
	return response.data.result;
}

async function updateProject(
	cloudflareProjectName: string,
	productionBranch: string,
	githubOwner: string,
	githubActualRepoName: string,
	buildCommand: string,
	destinationDir: string,
	rootDir: string
): Promise<PagesProject> {
	const envVarsPayload = getEnvVarsPayload();
	const payload: Partial<PagesProject> & {
		source: PagesProjectSource;
		build_config: PagesProjectBuildConfig;
		deployment_configs: PagesProjectDeploymentConfigs;
	} = {
		production_branch: productionBranch,
		source: {
			type: "github" as const,
			config: {
				owner: githubOwner,
				repo_name: githubActualRepoName,
				production_branch: productionBranch,
				deployments_enabled: true,
				pr_comments_enabled: true,
			},
		},
		build_config: {
			build_command: buildCommand,
			destination_dir: destinationDir,
			root_dir: rootDir,
			build_caching: true,
		},
		deployment_configs: {
			production: {
				env_vars: envVarsPayload,
			},
			preview: {
				env_vars: envVarsPayload,
			},
		},
	};
	const response = await axios.patch(
		`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/pages/projects/${cloudflareProjectName}`,
		payload,
		{
			headers: {
				Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
				"Content-Type": "application/json",
			},
		}
	);
	return response.data.result;
}

async function createDeployment(projectName: string): Promise<unknown> {
	// Define a proper type for deployment result if known
	const response = await axios.post(
		`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/pages/projects/${projectName}/deployments`,
		{}, // Empty body triggers a new deployment from the configured production branch
		{
			headers: {
				Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
				"Content-Type": "application/json",
			},
		}
	);
	return response.data.result;
}

async function addDomain(
	projectName: string,
	customDomain: string
): Promise<unknown> {
	// Define a proper type for domain result if known
	const response = await axios.post(
		`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/pages/projects/${projectName}/domains`,
		{ name: customDomain }, // API expects 'name' for custom domain
		{
			headers: {
				Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
				"Content-Type": "application/json",
			},
		}
	);
	return response.data.result;
}

async function getDeployments(projectName: string): Promise<PagesDeployment[]> {
	const response = await axios.get(
		`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/pages/projects/${projectName}/deployments`,
		{
			headers: {
				Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
				"Content-Type": "application/json",
			},
		}
	);
	return response.data.result;
}

async function getLatestDeployment(
	projectName: string
): Promise<PagesDeployment | null> {
	try {
		const deployments = await getDeployments(projectName);
		if (deployments.length === 0) {
			return null;
		}
		// Deployments are usually returned in descending order by creation date
		return deployments[0];
	} catch (error) {
		console.error(`Error getting latest deployment for ${projectName}:`, error);
		return null;
	}
}

async function retryDeployment(
	projectName: string,
	deploymentId: string
): Promise<PagesDeployment> {
	const response = await axios.post(
		`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/pages/projects/${projectName}/deployments/${deploymentId}/retry`,
		{}, // Empty body for retry
		{
			headers: {
				Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
				"Content-Type": "application/json",
			},
		}
	);
	return response.data.result;
}

// deployToCloudflare parameters:
// siteName: The GitHub repository name (e.g., "my-docusaurus-site")
// repoName: The GitHub repository full name, but with '/' replaced by '-' (e.g., "owner-my-docusaurus-site")
async function deployToCloudflare(
	githubRepoName: string,
	githubFullNameWithDash: string
): Promise<PagesProject> {
	const cloudflareProjectName = githubRepoName; // Use GitHub repo name as Cloudflare project name for simplicity

	// Extract GitHub owner: Assumes format "owner-repo-name" for githubFullNameWithDash
	const githubOwner = githubFullNameWithDash.substring(
		0,
		githubFullNameWithDash.indexOf("-")
	);
	const actualGithubRepoName = githubRepoName; // This is the repo_name for source.config

	const productionBranch = "main";
	// Updated buildCommand
	const buildCommand = "pnpm install --no-frozen-lockfile && npm run build";
	const destinationDir = "build";
	const rootDir = "/";

	// Define expected environment variables for checking
	const expectedEnvVars: PagesEnvVarSettings = {
		SKIP_DEPENDENCY_INSTALL: {
			type: "plain_text",
			value: SKIP_DEPENDENCY_INSTALL_VALUE,
		},
		DOCUSAURUS_ALGOLIA_ADMIN_KEY: {
			type: "secret_text", // Value is not checked for secrets, only presence and type
		},
		DOCUSAURUS_ALGOLIA_APP_ID: {
			type: "plain_text",
			value: ALGOLIA_APP_ID_VALUE,
		},
	};

	// Derive custom domain base
	const domainBase = githubRepoName
		.replace(/^-?docs-?/gi, "")
		.replace(/-?docs-?$/gi, "");
	const customDomain = `${domainBase}.writedocs.io`;

	try {
		let projectDetails: PagesProject;

		try {
			projectDetails = await getProject(cloudflareProjectName);
			console.log(
				`Project ${cloudflareProjectName} already exists. Verifying configuration...`
			);

			const currentSourceConfig = projectDetails.source?.config;
			const currentBuildConfig = projectDetails.build_config;
			const currentDeploymentConfigs = projectDetails.deployment_configs;

			const needsGitSourceUpdate =
				!currentSourceConfig ||
				currentSourceConfig.owner !== githubOwner ||
				currentSourceConfig.repo_name !== actualGithubRepoName ||
				currentSourceConfig.production_branch !== productionBranch ||
				currentSourceConfig.deployments_enabled !== true;

			const needsBuildConfigUpdate =
				!currentBuildConfig ||
				currentBuildConfig.build_command !== buildCommand ||
				currentBuildConfig.destination_dir !== destinationDir ||
				currentBuildConfig.root_dir !== rootDir ||
				currentBuildConfig.build_caching !== true;

			const needsProdBranchUpdate =
				projectDetails.production_branch !== productionBranch;

			const checkEnvVars = (
				envConfig: PagesDeploymentEnvConfig | undefined
			): boolean => {
				if (!envConfig?.env_vars) return true; // Needs update if no env_vars object
				for (const key in expectedEnvVars) {
					const expected = expectedEnvVars[key];
					const actual = envConfig.env_vars[key];
					if (!actual || actual.type !== expected.type) return true;
					// For plain_text, also check value. For secret_text, only presence and type are checked.
					if (expected.type === "plain_text" && actual.value !== expected.value)
						return true;
				}
				return false; // All expected env vars are present and correct
			};

			const needsDeploymentConfigUpdate =
				!currentDeploymentConfigs ||
				checkEnvVars(currentDeploymentConfigs.production) ||
				checkEnvVars(currentDeploymentConfigs.preview);

			if (
				needsGitSourceUpdate ||
				needsBuildConfigUpdate ||
				needsProdBranchUpdate ||
				needsDeploymentConfigUpdate
			) {
				console.log(
					`Updating project ${cloudflareProjectName} with correct Git source, build configuration, production branch, and deployment configs.`
				);
				projectDetails = await updateProject(
					cloudflareProjectName,
					productionBranch,
					githubOwner,
					actualGithubRepoName,
					buildCommand,
					destinationDir,
					rootDir
				);
				console.log(`Project ${cloudflareProjectName} updated successfully.`);
			} else {
				console.log(
					`Project ${cloudflareProjectName} configuration is up to date.`
				);
			}
		} catch (error) {
			if (
				axios.isAxiosError(error) &&
				error.response &&
				error.response.status === 404
			) {
				console.log(
					`Project ${cloudflareProjectName} not found. Creating new project.`
				);
				projectDetails = await createProject(
					cloudflareProjectName,
					productionBranch,
					githubOwner,
					actualGithubRepoName,
					buildCommand,
					destinationDir,
					rootDir
				);
				console.log(
					`Project ${cloudflareProjectName} created successfully with ID: ${projectDetails.id}`
				);
			} else {
				console.error(
					`Error fetching project ${cloudflareProjectName}:`,
					axios.isAxiosError(error)
						? error.response?.data
						: (error as Error).message
				);
				throw error; // Re-throw unexpected errors
			}
		}

		// Wrangler CLI block is removed.

		console.log(
			`Triggering deployment for project ${cloudflareProjectName}...`
		);
		await createDeployment(cloudflareProjectName);
		console.log(
			`Deployment successfully triggered for ${cloudflareProjectName}.`
		);

		try {
			console.log(
				`Attempting to add/verify custom domain ${customDomain} to project ${cloudflareProjectName}...`
			);
			await addDomain(cloudflareProjectName, customDomain);
			console.log(
				`Custom domain ${customDomain} processed for project ${cloudflareProjectName}.`
			);
		} catch (domainError) {
			if (
				axios.isAxiosError(domainError) &&
				domainError.response &&
				domainError.response.data?.errors?.[0]?.code === 8000019
			) {
				// Error code 8000019: "A domain with that name already exists."
				console.warn(
					`Custom domain ${customDomain} already exists for project ${cloudflareProjectName}.`
				);
			} else {
				console.warn(
					`Warning processing domain ${customDomain} for project ${cloudflareProjectName}:`,
					axios.isAxiosError(domainError)
						? domainError.response?.data
						: (domainError as Error).message
				);
			}
		}

		return projectDetails;
	} catch (error) {
		const errorMessage = axios.isAxiosError(error)
			? JSON.stringify(error.response?.data)
			: (error as Error).message;
		console.error(
			`Failed deployment process for ${cloudflareProjectName}: ${errorMessage}`
		);
		// Ensure the error message propagated is useful
		const finalErrorMessage =
			(axios.isAxiosError(error)
				? error.response?.data?.errors?.[0]?.message
				: null) ||
			errorMessage ||
			"Failed deployment to Cloudflare Pages.";
		throw new Error(
			`Failed deployment to Cloudflare Pages for ${cloudflareProjectName}. Details: ${finalErrorMessage}`
		);
	}
}

async function findCloudflareProjectByRepo(
	repoName: string
): Promise<PagesProject | undefined> {
	// This function expects repoName to be the actual GitHub repository name (e.g., "my-docs")
	// not "owner-repo" or "owner/repo".
	// The matching logic needs to be robust if multiple projects could map to one repo name under different owners.
	// For now, this assumes a direct mapping or that repoName is unique enough.
	try {
		const response = await axios.get(
			`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/pages/projects`,
			{
				headers: {
					Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
				},
				// Add pagination parameters if you have many projects, e.g., per_page: 100
			}
		);
		// The API for listing projects returns an array of project objects.
		// Each project object has a `source.config.repo_name` field.
		const projects: PagesProject[] = response.data.result;
		const project = projects.find(
			(proj) => proj.source?.config?.repo_name === repoName
			// Add && proj.source?.config?.owner === expectedOwner if owner is known and needed for uniqueness
		);

		if (project) {
			return project;
		}
		// No throw here, return undefined if not found by this specific repoName matching.
		// The caller can decide if "not found" is an error.
		console.log(
			`Project for repository ${repoName} not found via findCloudflareProjectByRepo.`
		);
		return undefined;
	} catch (error) {
		const msg = axios.isAxiosError(error)
			? JSON.stringify(error.response?.data)
			: (error as Error).message;
		console.error(`Error finding project by repo name ${repoName}: ${msg}`);
		throw error; // Re-throw as this indicates an issue with the API call itself.
	}
}

// Interface for Web Analytics Site Response
interface WebAnalyticsSite {
	site_tag: string;
	site_token: string;
	host: string;
	created: string;
	auto_install: boolean;
	snippet?: string;
	is_host_regex?: boolean;
}

interface WebAnalyticsListResponse {
	success: boolean;
	result: WebAnalyticsSite[];
	errors: unknown[];
	messages: unknown[];
	result_info: unknown;
}

interface ProjectInfo {
	siteName: string | undefined;
	siteTag: string | undefined;
	hasWebAnalytics: boolean | undefined;
}

/**
 * Busca informações do Web Analytics para um projeto específico
 * @param projectName Nome do projeto para buscar
 * @returns siteTag e host se encontrado, null caso contrário
 */
async function fetchWebAnalyticsSite(
	projectName: string
): Promise<{ siteTag: string; host: string } | null> {
	try {
		const response = await axios.get(
			`https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/rum/site_info/list`,
			{
				headers: {
					Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
					"Content-Type": "application/json",
				},
			}
		);

		if (!response.data.success) {
			return null;
		}

		const data = response.data as WebAnalyticsListResponse;

		// Logic to find the best matching site
		const keywords = projectName
			.split(/[-_.]/)
			.filter((word) => word.length > 2);

		let bestSite: WebAnalyticsSite | undefined;
		let bestScore = 0;
		const MINIMUM_CONFIDENCE_SCORE = 1000;

		data.result?.forEach((site: WebAnalyticsSite) => {
			const hostString = site.host || "";
			let score = 0;

			if (hostString.includes(projectName)) {
				score += MINIMUM_CONFIDENCE_SCORE;
			}

			const matchingKeywords = keywords.filter((keyword) =>
				hostString.toLowerCase().includes(keyword.toLowerCase())
			);
			score += matchingKeywords.length * 10;

			if (score > bestScore) {
				bestSite = site;
				bestScore = score;
			}
		});

		if (bestScore >= MINIMUM_CONFIDENCE_SCORE && bestSite) {
			return {
				siteTag: bestSite.site_tag,
				host: bestSite.host,
			};
		}

		return null;
	} catch (error) {
		console.error(
			`Error fetching Web Analytics for project ${projectName}:`,
			error
		);
		return null;
	}
}

/**
 * Busca informações do projeto no Cloudflare Pages
 * Implementa o fluxo: primeiro tenta Web Analytics, se falhar busca projeto Pages
 * @param projectName Nome do projeto
 * @returns Informações do projeto com flag hasWebAnalytics
 */
async function fetchProjectInfo(projectName: string): Promise<ProjectInfo> {
	// 1. Primeiro tenta buscar Web Analytics (apenas 1 request)
	const webAnalyticsResult = await fetchWebAnalyticsSite(projectName);

	if (webAnalyticsResult) {
		return {
			siteTag: webAnalyticsResult.siteTag,
			siteName: webAnalyticsResult.host,
			hasWebAnalytics: true,
		};
	}

	// 2. Se Web Analytics falhou, busca informações do projeto Pages (apenas 1 request)
	try {
		// Primeiro tenta buscar por nome exato do projeto
		const project = await getProject(projectName);

		if (project && project.subdomain) {
			return {
				siteName: `${project.subdomain}.pages.dev`,
				siteTag: undefined,
				hasWebAnalytics: false,
			};
		}
	} catch (error) {
		console.error(`Error fetching project ${projectName}:`, error);
		// Se não encontrou por nome exato, tenta buscar por repo
		try {
			const project = await findCloudflareProjectByRepo(projectName);

			if (project && project.subdomain) {
				return {
					siteName: `${project.subdomain}.pages.dev`,
					siteTag: undefined,
					hasWebAnalytics: false,
				};
			}
		} catch (repoError) {
			console.error(
				`Error finding project by repo name ${projectName}:`,
				repoError
			);
		}
	}

	// Se nenhum método funcionou, retorna informações básicas
	return {
		siteName: undefined,
		siteTag: undefined,
		hasWebAnalytics: false,
	};
}

export {
	deployToCloudflare,
	findCloudflareProjectByRepo,
	getProject,
	getDeployments,
	getLatestDeployment,
	retryDeployment,
	fetchProjectInfo,
};
