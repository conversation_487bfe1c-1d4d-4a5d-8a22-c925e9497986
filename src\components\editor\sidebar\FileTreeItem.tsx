"use client";

import React, { useState, useRef } from "react";
import {
  ChevronRight,
  ChevronDown,
  File,
  Folder,
  Folder<PERSON>pen,
  FileText,
  <PERSON>u,
  Minus,
} from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import type { TreeItemProps, TreeNode } from "./types";
import ContextMenu from "./ContextMenu";

const getNodeIcon = (node: TreeNode) => {
  switch (node.type) {
    case "navbar":
      // Diferencia entre navbar dropdown e navbar item
      if (node.data?.type === "navbar-dropdown") {
        return <Menu className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
      } else {
        // navbar-item ou navbar-dropdown-item
        return <Minus className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
      }
    case "sidebar":
      return node.isExpanded ? (
        <FolderOpen className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
      ) : (
        <Folder className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
      );
    case "category":
      return node.isExpanded ? (
        <FolderOpen className="w-4 h-4 text-orange-600 dark:text-orange-400" />
      ) : (
        <Folder className="w-4 h-4 text-orange-600 dark:text-orange-400" />
      );
    case "group":
      return node.isExpanded ? (
        <FolderOpen className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
      ) : (
        <Folder className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
      );
    case "page":
      return <FileText className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    case "subpage":
      return <File className="w-4 h-4 text-gray-500 dark:text-gray-500" />;
    default:
      return <File className="w-4 h-4 text-gray-400 dark:text-gray-500" />;
  }
};

const FileTreeItem: React.FC<TreeItemProps> = ({
  node,
  level,
  fullTree,
  onToggle,
  onDrop,
  draggedItem,
  setDraggedItem,
  createPageLink,
  handlePageClick,
  getItemClasses,
  onCreateItem,
  onRenameItem,
  onDeleteItem,
  editingNodeId,
  setEditingNodeId,
}) => {
  const [dragOver, setDragOver] = useState<
    "inside" | "before" | "after" | null
  >(null);
  const [editingName, setEditingName] = useState("");
  const itemRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const isEditing = editingNodeId === node.id;

  // Focus no input quando entra em modo de edição
  React.useEffect(() => {
    if (isEditing && inputRef.current) {
      setEditingName(node.name); // Inicializar o nome quando começar a editar
      // Usar requestAnimationFrame para garantir que o input seja renderizado antes de focar
      requestAnimationFrame(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          inputRef.current.select();
        }
      });
    }
  }, [isEditing, node.name]);

  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("text/plain", node.id);
    setDraggedItem(node.id);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();

    if (!itemRef.current || draggedItem === node.id) return;

    // Check if this would be an invalid drop (dragging into self)
    if (draggedItem && draggedItem === node.id) {
      setDragOver(null);
      return;
    }

    // NOVA RESTRIÇÃO: Proibir drops na raiz do FileTree (level 0) - EXCETO para navbar items DE RAIZ
    if (level === 0) {
      const draggedNode = findDraggedNode(draggedItem);
      // Permitir apenas navbar items DE RAIZ serem movidos na raiz
      if (
        !draggedNode ||
        draggedNode.type !== "navbar" ||
        draggedNode.data?.type === "navbar-dropdown-item"
      ) {
        setDragOver(null);
        return;
      }
    }

    // NOVA RESTRIÇÃO: Verificar tipos de drops permitidos - APENAS PARA POSIÇÃO "INSIDE"
    const draggedNode = findDraggedNode(draggedItem);
    if (draggedNode) {
      // Sidebars não podem ser arrastados
      if (draggedNode.type === "sidebar") {
        setDragOver(null);
        return;
      }
    }

    const rect = itemRef.current.getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    // Validações específicas para diferentes posições
    const draggedNodeForPosition = findDraggedNode(draggedItem);

    if (y < height * 0.25) {
      // Posição "before"
      // NOVA VALIDAÇÃO: Verificar se "before" é uma posição válida
      if (
        draggedNodeForPosition &&
        !isValidPositionForNode(draggedNodeForPosition, node, "before")
      ) {
        setDragOver(null);
        return;
      }
      setDragOver("before");
    } else if (y > height * 0.75) {
      // Posição "after"
      // NOVA VALIDAÇÃO: Verificar se "after" é uma posição válida
      if (
        draggedNodeForPosition &&
        !isValidPositionForNode(draggedNodeForPosition, node, "after")
      ) {
        setDragOver(null);
        return;
      }
      setDragOver("after");
    } else {
      // Posição "inside" - apenas para containers que podem ter filhos
      if (node.children && node.children.length >= 0 && canHaveChildren) {
        // Validar se "inside" é permitido
        if (
          draggedNodeForPosition &&
          !isValidPositionForNode(draggedNodeForPosition, node, "inside")
        ) {
          setDragOver(null);
          return;
        }
        setDragOver("inside");
      } else {
        setDragOver(null);
      }
    }
  };

  // Função auxiliar para encontrar o nó sendo arrastado
  const findDraggedNode = (draggedId: string | null): TreeNode | null => {
    if (!draggedId || !fullTree) return null;

    const findNodeInTree = (nodes: TreeNode[], id: string): TreeNode | null => {
      for (const node of nodes) {
        if (node.id === id) return node;
        if (node.children) {
          const found = findNodeInTree(node.children, id);
          if (found) return found;
        }
      }
      return null;
    };

    return findNodeInTree(fullTree, draggedId);
  };

  // Função auxiliar para validar se uma posição é válida para um tipo de nó
  const isValidPositionForNode = (
    draggedNode: TreeNode,
    targetNode: TreeNode,
    position: "inside" | "before" | "after"
  ): boolean => {
    // Para posição "inside" - validações mais restritivas
    if (position === "inside") {
      // Arquivos só podem ir inside de categories ou groups
      if (draggedNode.type === "page" || draggedNode.type === "subpage") {
        return targetNode.type === "category" || targetNode.type === "group";
      }

      // Categories PODEM ir inside de navbar items (todos que têm sidebarRef ou são dropdown)
      // OU dentro de outras categories (serão transformadas em groups)
      if (draggedNode.type === "category") {
        return (
          (targetNode.type === "navbar" &&
            (!!targetNode.data?.sidebarRef ||
              targetNode.data?.type === "navbar-dropdown-item")) ||
          targetNode.type === "category" ||
          targetNode.type === "group"
        );
      }

      // Groups PODEM ir inside de categories, outros groups OU navbar items (todos que têm sidebarRef ou são dropdown)
      if (draggedNode.type === "group") {
        return (
          targetNode.type === "category" ||
          targetNode.type === "group" ||
          (targetNode.type === "navbar" &&
            (!!targetNode.data?.sidebarRef ||
              targetNode.data?.type === "navbar-dropdown-item"))
        );
      }

      // Navbar items podem ir dentro de navbar dropdowns
      if (draggedNode.type === "navbar") {
        // Navbar items de raiz podem ir dentro de dropdowns
        if (
          draggedNode.data?.type === "navbar-item" &&
          targetNode.type === "navbar" &&
          targetNode.data?.type === "navbar-dropdown"
        ) {
          return true;
        }
      }

      return false;
    }

    // Para posições "before" e "after" - validações mais permissivas
    if (position === "before" || position === "after") {
      // NOVA VALIDAÇÃO: Verificar se o movimento resultaria em colocar item como filho direto de navbar
      const findNodeInFullTree = (
        nodes: TreeNode[],
        id: string
      ): TreeNode | null => {
        for (const node of nodes) {
          if (node.id === id) return node;
          if (node.children) {
            const found = findNodeInFullTree(node.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      const targetParent = findNodeInFullTree(
        fullTree,
        targetNode.parentId || ""
      );
      if (targetParent && targetParent.type === "navbar") {
        // Se o target está dentro de um navbar, verificar se o draggedNode pode ser filho de navbar
        if (draggedNode.type === "page" || draggedNode.type === "subpage") {
          console.log(
            "❌ Movimento before/after resultaria em arquivo como filho direto de navbar"
          );
          return false; // Arquivos não podem ser filhos diretos de navbar
        }
      }

      // NOVA REGRA MELHORADA: Navbar items só podem ficar before/after de outros navbar items DO MESMO NÍVEL
      if (draggedNode.type === "navbar" && targetNode.type === "navbar") {
        // Verificar se ambos são do mesmo tipo (raiz ou dropdown-item)
        const draggedIsDropdownItem =
          draggedNode.data?.type === "navbar-dropdown-item";
        const targetIsDropdownItem =
          targetNode.data?.type === "navbar-dropdown-item";

        // Ambos devem ser do mesmo tipo (ambos raiz ou ambos dropdown-item)
        if (draggedIsDropdownItem === targetIsDropdownItem) {
          // Se ambos são dropdown-items, verificar se estão no mesmo dropdown
          if (draggedIsDropdownItem && targetIsDropdownItem) {
            return (
              draggedNode.data?.dropdownIndex === targetNode.data?.dropdownIndex
            );
          }
          return true; // Ambos são de raiz
        }
        return false; // Tipos diferentes (um é raiz, outro é dropdown-item)
      }

      // Arquivos PODEM ficar before/after de outros arquivos, groups, categories (mesmo nível hierárquico)
      if (draggedNode.type === "page" || draggedNode.type === "subpage") {
        // Permitir movimento entre arquivos, groups, e até categories (para mudança de nível)
        return (
          targetNode.type === "page" ||
          targetNode.type === "subpage" ||
          targetNode.type === "group" ||
          targetNode.type === "category"
        );
      }

      // Categories podem ficar before/after de outras categories, groups ou páginas
      if (draggedNode.type === "category") {
        return (
          targetNode.type === "category" ||
          targetNode.type === "group" ||
          targetNode.type === "page" ||
          targetNode.type === "subpage"
        );
      }

      // Groups podem ficar before/after de outros groups, páginas ou categories
      if (draggedNode.type === "group") {
        return (
          targetNode.type === "group" ||
          targetNode.type === "page" ||
          targetNode.type === "subpage" ||
          targetNode.type === "category"
        );
      }
    }

    return false;
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!itemRef.current?.contains(e.relatedTarget as Node)) {
      setDragOver(null);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const draggedId = e.dataTransfer.getData("text/plain");

    if (draggedId && draggedId !== node.id && dragOver) {
      onDrop(draggedId, node.id, dragOver);
    }

    setDragOver(null);
    setDraggedItem(null);
  };

  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOver(null);
  };

  const canHaveChildren = ["navbar", "sidebar", "category", "group"].includes(
    node.type
  );

  const handleContainerClick = (e: React.MouseEvent) => {
    // Se está editando, não fazer nada
    if (isEditing) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    handleClick();
  };

  const handleClick = () => {
    if (isEditing) return; // Não fazer nada se estiver editando

    if (canHaveChildren) {
      onToggle(node.id);
    } else if (node.path) {
      const href = createPageLink(node.path);
      handlePageClick(href);
    }
  };

  const handleCancelEdit = () => {
    setEditingNodeId(null);
    setEditingName("");
  };

  const handleConfirmEdit = () => {
    if (editingName.trim() && editingName.trim() !== node.name) {
      onRenameItem(node, editingName.trim());
    }
    setEditingNodeId(null);
    setEditingName("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleConfirmEdit();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  const handleInputBlur = (e: React.FocusEvent) => {
    // Verificar se o foco realmente saiu do input
    // Se o relatedTarget for null ou não estiver dentro do mesmo contexto, confirmar
    const relatedTarget = e.relatedTarget as HTMLElement;

    // Se não há relatedTarget ou se está indo para fora do tree item, confirmar
    if (!relatedTarget || !itemRef.current?.contains(relatedTarget)) {
      // Pequeno delay apenas para clicks fora (relatedTarget null)
      if (!relatedTarget) {
        setTimeout(() => {
          if (editingNodeId === node.id) {
            handleConfirmEdit();
          }
        }, 100);
      } else {
        handleConfirmEdit();
      }
    }
  };

  const renderContent = () => {
    // Renderizar input de edição se estiver editando
    if (isEditing) {
      return (
        <div className="flex items-center gap-2 flex-1">
          {canHaveChildren && (
            <button className="p-0.5 rounded hover:bg-gray-200/60 dark:hover:bg-gray-600/60 transition-colors duration-150 flex-shrink-0">
              {node.isExpanded ? (
                <ChevronDown className="w-3 h-3 text-gray-500 dark:text-gray-400" />
              ) : (
                <ChevronRight className="w-3 h-3 text-gray-500 dark:text-gray-400" />
              )}
            </button>
          )}

          {!canHaveChildren && <div className="w-4 flex-shrink-0" />}

          <div className="flex-shrink-0 flex items-center justify-center">
            {getNodeIcon(node)}
          </div>

          <Input
            ref={inputRef}
            value={editingName}
            onChange={(e) => setEditingName(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleInputBlur}
            onMouseDown={(e) => e.stopPropagation()}
            onFocus={(e) => e.stopPropagation()}
            className="h-6 text-sm py-0 px-1 border-blue-500 focus:border-blue-600 min-w-0 flex-1"
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      );
    }

    if (node.path && (node.type === "page" || node.type === "subpage")) {
      const href = createPageLink(node.path);
      const itemClasses = getItemClasses(href);

      return (
        <Link
          href={href}
          onClick={() => handlePageClick(href)}
          className={cn(
            "flex items-center gap-2 flex-1 px-2 py-1.5 rounded-md text-sm transition-colors duration-150",
            itemClasses
          )}
        >
          <div className="flex-shrink-0 flex items-center justify-center">
            {getNodeIcon(node)}
          </div>
          <span className="truncate min-w-0">{node.name}</span>
        </Link>
      );
    }

    return (
      <div className="flex items-center gap-2 flex-1">
        {canHaveChildren && (
          <button className="p-0.5 rounded hover:bg-gray-200/60 dark:hover:bg-gray-600/60 transition-colors duration-150 flex-shrink-0">
            {node.isExpanded ? (
              <ChevronDown className="w-3 h-3 text-gray-500 dark:text-gray-400" />
            ) : (
              <ChevronRight className="w-3 h-3 text-gray-500 dark:text-gray-400" />
            )}
          </button>
        )}

        {!canHaveChildren && <div className="w-4 flex-shrink-0" />}

        <div className="flex-shrink-0 flex items-center justify-center">
          {getNodeIcon(node)}
        </div>

        <span className="text-sm text-gray-700 dark:text-gray-300 truncate min-w-0">
          {node.name}
        </span>
      </div>
    );
  };

  return (
    <div className="relative">
      {dragOver === "before" && (
        <div
          className="h-0.5 bg-blue-500 mx-2 rounded-full"
          style={{ marginLeft: `${level * 12 + 8}px` }}
        />
      )}

      <ContextMenu
        node={node}
        onCreateItem={onCreateItem}
        onRenameItem={onRenameItem}
        onDeleteItem={onDeleteItem}
        editingNodeId={editingNodeId}
        setEditingNodeId={setEditingNodeId}
      >
        <div
          ref={itemRef}
          className={cn(
            "flex items-center py-1 px-2 rounded-md cursor-pointer select-none relative transition-all duration-150 group",
            "hover:bg-gray-100/80 dark:hover:bg-gray-700/50",

            // Dragged state
            draggedItem === node.id &&
              "opacity-50 bg-gray-200/50 dark:bg-gray-600/50",

            // Drop target state
            dragOver === "inside" &&
              canHaveChildren &&
              "bg-blue-100/80 dark:bg-blue-900/30 ring-1 ring-blue-300/50 dark:ring-blue-600/50"
          )}
          style={{
            paddingLeft: `${level * 16 + 8}px`,
          }}
          draggable
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onDragEnd={handleDragEnd}
          onClick={handleContainerClick}
        >
          {renderContent()}
        </div>
      </ContextMenu>

      {dragOver === "after" && (
        <div
          className="h-0.5 bg-blue-500 mx-2 rounded-full"
          style={{ marginLeft: `${level * 12 + 8}px` }}
        />
      )}

      {canHaveChildren && node.isExpanded && node.children && (
        <div className="relative">
          {/* Linha de conexão sutil para hierarquia */}
          {level >= 0 && (
            <div
              className="absolute left-0 top-0 bottom-0 w-px bg-gray-200/60 dark:bg-gray-600/40"
              style={{ left: `${level * 16 + 16}px` }}
            />
          )}

          {node.children.map((child) => (
            <FileTreeItem
              key={child.id}
              node={child}
              level={level + 1}
              fullTree={fullTree}
              onToggle={onToggle}
              onDrop={onDrop}
              draggedItem={draggedItem}
              setDraggedItem={setDraggedItem}
              createPageLink={createPageLink}
              handlePageClick={handlePageClick}
              getItemClasses={getItemClasses}
              onCreateItem={onCreateItem}
              onRenameItem={onRenameItem}
              onDeleteItem={onDeleteItem}
              editingNodeId={editingNodeId}
              setEditingNodeId={setEditingNodeId}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default FileTreeItem;
