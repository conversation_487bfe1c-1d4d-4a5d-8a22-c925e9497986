"use server";

import { createClient } from "@/utils/supabase/server";
import type { Project } from "@/contexts/ProjectContext/types";

// Interface for the raw data from the 'project_user' table
interface ProjectUserRecord {
	user_id: string;
	project_id: number;
	role: string;
	is_creator: boolean;
	// Add other fields if select("*") returns more and they are used.
}

// Interface for the raw data from the 'projects' table
interface ProjectRecord {
	id: number;
	project_name: string;
	owner_id: string;
	website_url: string;
	deploy_repo_url: string;
	source_repo_url: string;
	company_name: string | null;
	auto_config: boolean;
	docusaurus_configs: unknown | null;
	plan_json?: { docsbot?: string };
	// Add other fields if select("*") returns more and they are used.
}

export async function getUserProjects(): Promise<Project[] | null> {
	const supabase = createClient();

	// Fetch data from project_user table
	const { data: projectUserRawData, error: projectUserError } = await supabase
		.from("project_user")
		.select("*");

	// Explicitly type the extracted data
	const projectUserData: ProjectUserRecord[] | null = projectUserRawData;

	if (projectUserError) {
		console.error(
			"Error fetching project_user data:",
			projectUserError.message
		);
		return null;
	}

	if (!projectUserData) {
		return []; // No user projects found, return empty array
	}

	const projectIds = projectUserData.map(
		(projectUser) => projectUser.project_id
	);

	if (projectIds.length === 0) {
		return []; // No project IDs to fetch details for
	}

	// Fetch data from projects table
	const { data: projectsRawData, error: projectsError } = await supabase
		.from("projects")
		.select("*")
		.in("id", projectIds);

	// Explicitly type the extracted data
	const projectsData: ProjectRecord[] | null = projectsRawData;

	if (projectsError) {
		console.error("Error fetching projects data:", projectsError.message);
		return null;
	}

	if (!projectsData) {
		// This case might indicate an issue if projectIds was not empty
		console.warn(
			"No project details found for provided IDs, though project_user entries exist."
		);
		return [];
	}

	const userProjects: Project[] = projectUserData
		.map((projectUserItem) => {
			const projectDetailsItem = projectsData.find(
				(p: ProjectRecord) => p.id === projectUserItem.project_id
			);

			if (!projectDetailsItem) {
				// This should ideally not happen if DB has referential integrity and all IDs were fetched
				console.warn(
					`Project details not found for project_id: ${projectUserItem.project_id}. Skipping this project.`
				);
				return null;
			}

			// Construct plan_json according to Project type
			const org = process.env.DOCSBOT_ORG;
			const plan_json_docsbot_value = projectDetailsItem.plan_json?.docsbot; // Assuming plan_json might come from DB
			const final_docsbot_value =
				org && plan_json_docsbot_value
					? `${org}/${plan_json_docsbot_value}`
					: plan_json_docsbot_value || "";

			// Combine the data ensuring it matches the Project interface
			// The Project interface expects 'id' from projects table and 'project_id' from project_user table.
			// It also expects fields like 'project_name' from projectDetailsItem.
			const combinedProject: Project = {
				...projectUserItem, // Contains user_id, project_id, role, is_creator
				...projectDetailsItem, // Contains id, project_name, owner_id, etc.
				plan_json: {
					docsbot: final_docsbot_value,
				},
				// Initialize Cloudflare data as undefined - will be populated by server-side layout
				siteTag: undefined,
				siteName: undefined,
				hasWebAnalytics: undefined,
			};
			return combinedProject;
		})
		.filter((project): project is Project => project !== null);

	return userProjects;
}
