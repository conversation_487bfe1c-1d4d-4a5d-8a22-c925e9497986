"use client";

import { use } from "react";
import PageDisplay from "@/components/editor/PageDisplay";

interface EditorDynamicPageProps {
  params: Promise<{
    projectId: string;
    pagePath: string[];
  }>;
}

export default function EditorDynamicPage({ params }: EditorDynamicPageProps) {
  const { projectId, pagePath: pagePathArray } = use(params);

  const pagePath =
    pagePathArray && pagePathArray.length > 0
      ? "/" + pagePathArray.join("/")
      : "/";

  if (!projectId) {
    return <p>Loading project information...</p>;
  }

  return <PageDisplay projectId={projectId} pagePath={pagePath} />;
}
