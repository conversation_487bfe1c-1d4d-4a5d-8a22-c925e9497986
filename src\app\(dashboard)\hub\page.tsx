"use client";

import Image from "next/image";
import { useProject, useUser } from "@/contexts";
import { Plus, Briefcase } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import writedocsLogoSvg from "@/assets/images/logoWriteDocs.svg";
import { useRouter } from "next/navigation";
import { HomeSidebar } from "@/components/home-sidebar";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { InviteCard } from "@/components/InviteCard";
import Link from "next/link";

export default function ProjectSelectorPage() {
  const {
    projects,
    isLoadingProjects,
    invites,
    acceptInvite,
    declineInvite,
    isLoadingInvites,
  } = useProject();
  const { userProfile } = useUser();
  const router = useRouter();
  const userName =
    userProfile?.fullName ||
    userProfile?.name ||
    userProfile?.email ||
    "<User>";

  return (
    <div className="flex h-screen w-full">
      <HomeSidebar />
      <div className="flex-1 overflow-y-auto p-8 md:px-8">
        <SidebarTrigger />
        <header className="mb-8 pt-2">
          <div className="mb-8">
            <Image
              src={writedocsLogoSvg}
              alt="Writedocs Logo"
              className="h-8 w-auto"
            />
          </div>
          {isLoadingProjects ? (
            <Skeleton className="h-9 w-1/3 mb-1" />
          ) : (
            <h1 className="text-3xl font-semibold text-gray-800 dark:text-white">
              Hello, {userName}
            </h1>
          )}
          <p className="text-gray-600 dark:text-gray-400">
            Welcome to your projects!
          </p>
        </header>

        <hr className="mb-8 border-gray-200 dark:border-gray-700" />

        {isLoadingProjects && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <Card key={index} className="transform transition-none">
                <CardHeader className="items-center text-center p-6">
                  <Skeleton className="w-12 h-12 rounded-full mb-3" />
                  <Skeleton className="h-6 w-3/4" />
                </CardHeader>
                <CardContent className="text-center p-0 pb-4">
                  <Skeleton className="h-4 w-1/2 mx-auto" />
                </CardContent>
              </Card>
            ))}
            {/* Skeleton for Create New Project Card */}
            <Card className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 transform transition-none">
              <CardHeader className="items-center text-center p-6">
                <Skeleton className="w-12 h-12 rounded-md mb-3" />
                <Skeleton className="h-6 w-3/4" />
              </CardHeader>
            </Card>
          </div>
        )}

        {!isLoadingProjects && projects.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {/* Invite Cards - displayed first */}
            {invites.map((invite) => (
              <InviteCard
                key={`invite-${invite.id}`}
                invite={invite}
                onAccept={acceptInvite}
                onDecline={declineInvite}
                isProcessing={isLoadingInvites}
              />
            ))}

            {/* Project Cards */}
            {projects.map((project) => (
              <Link
                href={`/${project.id}/dashboard`}
                key={project.id.toString()}
              >
                <Card className="hover:shadow-lg transition-shadow duration-200 ease-in-out transform hover:-translate-y-1 cursor-pointer">
                  <CardHeader className="items-center text-center p-6">
                    <Briefcase className="w-12 h-12 text-blue-600 dark:text-blue-400 mb-3" />
                    <CardTitle className="text-xl h-14 flex items-center justify-center overflow-hidden">
                      {project.project_name}
                    </CardTitle>
                  </CardHeader>
                  {project.website_url && (
                    <CardContent className="text-center p-0 pb-4">
                      <CardDescription className="truncate">
                        {project.website_url}
                      </CardDescription>
                    </CardContent>
                  )}
                </Card>
              </Link>
            ))}

            {/* Create New Project Card */}
            <Card
              onClick={() => router.push("/new-project")}
              className="flex flex-col items-center justify-center hover:shadow-lg transition-shadow duration-200 ease-in-out transform hover:-translate-y-1 cursor-pointer border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 bg-gray-50 dark:bg-gray-700"
            >
              <CardHeader className="items-center text-center p-6">
                <Plus className="w-12 h-12 text-gray-400 dark:text-gray-500 mb-3" />
                <CardTitle className="text-xl text-gray-700 dark:text-white h-14 flex items-center justify-center overflow-hidden">
                  Create new project
                </CardTitle>
              </CardHeader>
            </Card>
          </div>
        )}

        {!isLoadingProjects && projects.length === 0 && (
          <>
            {/* Show invite cards even when no projects exist */}
            {invites.length > 0 && (
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                  Pending Invitations
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {invites.map((invite) => (
                    <InviteCard
                      key={`invite-${invite.id}`}
                      invite={invite}
                      onAccept={acceptInvite}
                      onDecline={declineInvite}
                      isProcessing={isLoadingInvites}
                    />
                  ))}
                </div>
              </div>
            )}

            <div className="flex flex-col items-center justify-center p-10 bg-white dark:bg-gray-800 rounded-lg shadow-md mt-8">
              <Briefcase className="w-20 h-20 text-gray-300 dark:text-gray-600 mb-6" />
              <h2 className="text-2xl font-semibold text-gray-700 dark:text-white mb-2">
                {invites.length > 0 ? "No Projects Yet" : "No Projects Yet"}
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mb-6 text-center">
                {invites.length > 0 ? (
                  "You have pending invitations above, or create your first project!"
                ) : (
                  <>
                    It looks like you haven&apos;t created any projects. <br />
                    Get started by creating your first one!
                  </>
                )}
              </p>
              {/* "Create New Project" button for empty state - can also be a Card */}
              <div
                onClick={() => router.push("/new-project")}
                className="flex flex-col items-center justify-center p-6 bg-blue-500 hover:bg-blue-600 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ease-in-out transform hover:-translate-y-1 cursor-pointer w-full max-w-xs"
              >
                <Plus className="w-8 h-8 mb-2" />
                <h2 className="text-lg font-semibold">Create new project</h2>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
