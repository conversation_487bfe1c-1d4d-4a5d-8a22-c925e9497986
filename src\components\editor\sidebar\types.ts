export interface TreeNode {
  id: string;
  name: string;
  type: "navbar" | "sidebar" | "category" | "page" | "subpage" | "group";
  children?: TreeNode[];
  isExpanded?: boolean;
  parentId?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
  path?: string;
}

export interface TreeItemProps {
  node: TreeNode;
  level: number;
  fullTree: TreeNode[];
  onToggle: (id: string) => void;
  onDrop: (
    draggedId: string,
    targetId: string,
    position: "inside" | "before" | "after"
  ) => void;
  draggedItem: string | null;
  setDraggedItem: (id: string | null) => void;
  createPageLink: (pagePath: string) => string;
  handlePageClick: (href: string) => void;
  getItemClasses: (href: string) => string;
  // Context menu handlers
  onCreateItem: (
    parentNode: TreeNode,
    type: "category" | "group" | "page" | "navbar-item" | "navbar-dropdown",
    name: string,
    path?: string
  ) => void;
  onRenameItem: (node: TreeNode, newName: string) => void;
  onDeleteItem: (node: TreeNode) => void;
  // Inline editing
  editingNodeId: string | null;
  setEditingNodeId: (id: string | null) => void;
}

export interface FileTreeProps {
  tree: TreeNode[];
  onToggle: (id: string) => void;
  onDrop: (
    draggedId: string,
    targetId: string,
    position: "inside" | "before" | "after"
  ) => void;
  draggedItem: string | null;
  setDraggedItem: (id: string | null) => void;
  createPageLink: (pagePath: string) => string;
  handlePageClick: (href: string) => void;
  getItemClasses: (href: string) => string;
  // Context menu handlers
  onCreateItem: (
    parentNode: TreeNode,
    type: "category" | "group" | "page" | "navbar-item" | "navbar-dropdown",
    name: string,
    path?: string
  ) => void;
  onRenameItem: (node: TreeNode, newName: string) => void;
  onDeleteItem: (node: TreeNode) => void;
  // Inline editing
  editingNodeId: string | null;
  setEditingNodeId: (id: string | null) => void;
}
