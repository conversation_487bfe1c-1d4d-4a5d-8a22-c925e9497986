import React from "react";
import {
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Bold,
  Italic,
  Code,
  Quote,
  Minus,
  AlignLeft,
  Settings,
  Table as TableIcon,
  Columns,
  Rows,
  X,
  Trash2,
  CreditCard,
  ChevronDown,
  Layers,
  Link,
  Info,
  Image as ImageIcon,
  Play,
  ArrowRight,
} from "lucide-react";

export const getCommandIcon = (title: string) => {
  const iconProps = "w-3.5 h-3.5";

  switch (title) {
    case "Metadata":
      return (
        <div className="w-5 h-5 rounded-md bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3 transition-colors">
          <Settings
            className={`${iconProps} text-blue-600 dark:text-blue-400`}
          />
        </div>
      );
    case "Text":
      return (
        <div className="w-5 h-5 rounded-md bg-slate-100 dark:bg-slate-700/60 flex items-center justify-center mr-3 transition-colors">
          <AlignLeft
            className={`${iconProps} text-slate-600 dark:text-slate-400`}
          />
        </div>
      );
    case "Heading 1":
      return (
        <div className="w-5 h-5 rounded-md bg-purple-100 dark:bg-purple-900/40 flex items-center justify-center mr-3 transition-colors">
          <Heading1
            className={`${iconProps} text-purple-600 dark:text-purple-400`}
          />
        </div>
      );
    case "Heading 2":
      return (
        <div className="w-5 h-5 rounded-md bg-purple-100 dark:bg-purple-900/40 flex items-center justify-center mr-3 transition-colors">
          <Heading2
            className={`${iconProps} text-purple-600 dark:text-purple-400`}
          />
        </div>
      );
    case "Heading 3":
      return (
        <div className="w-5 h-5 rounded-md bg-purple-100 dark:bg-purple-900/40 flex items-center justify-center mr-3 transition-colors">
          <Heading3
            className={`${iconProps} text-purple-600 dark:text-purple-400`}
          />
        </div>
      );
    case "Bullet List":
      return (
        <div className="w-5 h-5 rounded-md bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3 transition-colors">
          <List className={`${iconProps} text-blue-600 dark:text-blue-400`} />
        </div>
      );
    case "Numbered List":
      return (
        <div className="w-5 h-5 rounded-md bg-indigo-100 dark:bg-indigo-900/40 flex items-center justify-center mr-3 transition-colors">
          <ListOrdered
            className={`${iconProps} text-indigo-600 dark:text-indigo-400`}
          />
        </div>
      );
    case "Table":
      return (
        <div className="w-5 h-5 rounded-md bg-cyan-100 dark:bg-cyan-900/40 flex items-center justify-center mr-3 transition-colors">
          <TableIcon
            className={`${iconProps} text-cyan-600 dark:text-cyan-400`}
          />
        </div>
      );
    case "Add Column":
      return (
        <div className="w-5 h-5 rounded-md bg-teal-100 dark:bg-teal-900/40 flex items-center justify-center mr-3 transition-colors">
          <Columns
            className={`${iconProps} text-teal-600 dark:text-teal-400`}
          />
        </div>
      );
    case "Add Row":
      return (
        <div className="w-5 h-5 rounded-md bg-teal-100 dark:bg-teal-900/40 flex items-center justify-center mr-3 transition-colors">
          <Rows className={`${iconProps} text-teal-600 dark:text-teal-400`} />
        </div>
      );
    case "Remove Column":
      return (
        <div className="w-5 h-5 rounded-md bg-red-100 dark:bg-red-900/40 flex items-center justify-center mr-3 transition-colors">
          <X className={`${iconProps} text-red-600 dark:text-red-400`} />
        </div>
      );
    case "Remove Row":
      return (
        <div className="w-5 h-5 rounded-md bg-red-100 dark:bg-red-900/40 flex items-center justify-center mr-3 transition-colors">
          <Minus className={`${iconProps} text-red-600 dark:text-red-400`} />
        </div>
      );
    case "Delete Table":
      return (
        <div className="w-5 h-5 rounded-md bg-red-100 dark:bg-red-900/40 flex items-center justify-center mr-3 transition-colors">
          <Trash2 className={`${iconProps} text-red-600 dark:text-red-400`} />
        </div>
      );
    case "Bold":
      return (
        <div className="w-5 h-5 rounded-md bg-orange-100 dark:bg-orange-900/40 flex items-center justify-center mr-3 transition-colors">
          <Bold
            className={`${iconProps} text-orange-600 dark:text-orange-400`}
          />
        </div>
      );
    case "Italic":
      return (
        <div className="w-5 h-5 rounded-md bg-orange-100 dark:bg-orange-900/40 flex items-center justify-center mr-3 transition-colors">
          <Italic
            className={`${iconProps} text-orange-600 dark:text-orange-400`}
          />
        </div>
      );
    case "Link":
      return (
        <div className="w-5 h-5 rounded-md bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3 transition-colors">
          <Link className={`${iconProps} text-blue-600 dark:text-blue-400`} />
        </div>
      );
    case "Code Block":
      return (
        <div className="w-5 h-5 rounded-md bg-green-100 dark:bg-green-900/40 flex items-center justify-center mr-3 transition-colors">
          <Code className={`${iconProps} text-green-600 dark:text-green-400`} />
        </div>
      );
    case "Card":
      return (
        <div className="w-5 h-5 rounded-md bg-pink-100 dark:bg-pink-900/40 flex items-center justify-center mr-3 transition-colors">
          <CreditCard
            className={`${iconProps} text-pink-600 dark:text-pink-400`}
          />
        </div>
      );
    case "Card List":
      return (
        <div className="w-5 h-5 rounded-md bg-purple-100 dark:bg-purple-900/40 flex items-center justify-center mr-3 transition-colors">
          <div className="grid grid-cols-2 gap-0.5 w-3 h-3">
            <div className="bg-purple-600 dark:bg-purple-400 rounded-sm"></div>
            <div className="bg-purple-600 dark:bg-purple-400 rounded-sm"></div>
            <div className="bg-purple-600 dark:bg-purple-400 rounded-sm"></div>
            <div className="bg-purple-600 dark:bg-purple-400 rounded-sm"></div>
          </div>
        </div>
      );
    case "Blockquote":
      return (
        <div className="w-5 h-5 rounded-md bg-emerald-100 dark:bg-emerald-900/40 flex items-center justify-center mr-3 transition-colors">
          <Quote
            className={`${iconProps} text-emerald-600 dark:text-emerald-400`}
          />
        </div>
      );
    case "Callout":
      return (
        <div className="w-5 h-5 rounded-md bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3 transition-colors">
          <Info className={`${iconProps} text-blue-600 dark:text-blue-400`} />
        </div>
      );
    case "Accordion":
      return (
        <div className="w-5 h-5 rounded-md bg-orange-100 dark:bg-orange-900/40 flex items-center justify-center mr-3 transition-colors">
          <ChevronDown
            className={`${iconProps} text-orange-600 dark:text-orange-400`}
          />
        </div>
      );
    case "Accordion Group":
      return (
        <div className="w-5 h-5 rounded-md bg-amber-100 dark:bg-amber-900/40 flex items-center justify-center mr-3 transition-colors">
          <Layers
            className={`${iconProps} text-amber-600 dark:text-amber-400`}
          />
        </div>
      );
    case "Steps":
      return (
        <div className="w-5 h-5 rounded-md bg-indigo-100 dark:bg-indigo-900/40 flex items-center justify-center mr-3 transition-colors">
          <ArrowRight
            className={`${iconProps} text-indigo-600 dark:text-indigo-400`}
          />
        </div>
      );
    case "Tabs":
      return (
        <div className="w-5 h-5 rounded-md bg-teal-100 dark:bg-teal-900/40 flex items-center justify-center mr-3 transition-colors">
          <div className="flex flex-col w-3 h-3">
            <div className="flex gap-0.5 mb-0.5">
              <div className="bg-teal-600 dark:bg-teal-400 rounded-sm w-1 h-1"></div>
              <div className="bg-teal-600 dark:bg-teal-400 rounded-sm w-1 h-1"></div>
              <div className="bg-teal-600 dark:bg-teal-400 rounded-sm w-1 h-1"></div>
            </div>
            <div className="bg-teal-600 dark:bg-teal-400 rounded-sm w-full flex-1"></div>
          </div>
        </div>
      );
    case "Image":
      return (
        <div className="w-5 h-5 rounded-md bg-violet-100 dark:bg-violet-900/40 flex items-center justify-center mr-3 transition-colors">
          <ImageIcon
            className={`${iconProps} text-violet-600 dark:text-violet-400`}
          />
        </div>
      );
    case "Video":
      return (
        <div className="w-5 h-5 rounded-md bg-red-100 dark:bg-red-900/40 flex items-center justify-center mr-3 transition-colors">
          <Play className={`${iconProps} text-red-600 dark:text-red-400`} />
        </div>
      );
    case "Horizontal Rule":
      return (
        <div className="w-5 h-5 rounded-md bg-slate-100 dark:bg-slate-700/60 flex items-center justify-center mr-3 transition-colors">
          <Minus
            className={`${iconProps} text-slate-600 dark:text-slate-400`}
          />
        </div>
      );
    default:
      return (
        <div className="w-5 h-5 rounded-md bg-slate-100 dark:bg-slate-700/60 flex items-center justify-center mr-3 transition-colors">
          <AlignLeft
            className={`${iconProps} text-slate-600 dark:text-slate-400`}
          />
        </div>
      );
  }
};
