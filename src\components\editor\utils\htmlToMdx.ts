import { unified } from "unified";
import rehypeParse from "rehype-parse";
import type { Options as RehypeRemarkOptions } from "rehype-remark";
import rehypeRemark from "rehype-remark";
import remarkStringify from "remark-stringify";
import remarkMdx from "remark-mdx";
import remarkFrontmatter from "remark-frontmatter";
import remarkGfm from "remark-gfm";
import { visit } from "unist-util-visit";
import type {
  Root as HastRoot,
  Element as HastElement,
  Properties,
  Parent as HastParent,
  Node as HastNode,
  Text as HastText,
  RootContent,
  ElementContent,
} from "hast";
import { toHtml } from "hast-util-to-html";
import type { VFile } from "vfile";
import type { BlockContent, DefinitionContent, Root as MdastRoot } from "mdast";
import { validateHtmlForMdx } from "./conversionValidators";

// ===== TYPES & CONSTANTS =====

interface TransformContext {
  frontmatter: Record<string, string>;
  componentMap: Map<string, HastElement>;
}

interface StepData {
  title?: string;
  content?: string;
  titleSize?: string;
  subSteps?: StepData[];
}

const SELF_CLOSING_TAGS = new Set([
  "br",
  "hr",
  "img",
  "input",
  "meta",
  "link",
  "area",
  "base",
  "col",
  "embed",
  "source",
  "track",
  "wbr",
]);

const FORMATTING_TAGS = new Set([
  "strong",
  "em",
  "s",
  "del",
  "b",
  "i",
  "u",
  "strike",
]);

const COMPONENT_TAGS = new Set([
  "card",
  "cardlist",
  "accordion",
  "accordion-group",
  "callout",
  "steps",
  "tabs",
  "image",
  "video",
]);

// ===== UTILITY FUNCTIONS =====

const decodeHtmlEntities = (text: string): string => {
  return text
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&amp;/g, "&");
};

const isEmptyNode = (node: HastElement): boolean => {
  return (
    node.children.length === 0 ||
    node.children.every((c) => c.type === "text" && !c.value.trim())
  );
};

const createJsxAttributes = (
  properties: Properties = {}
): Array<{
  type: "mdxJsxAttribute";
  name: string;
  value: string | { type: "mdxJsxAttributeValueExpression"; value: string };
}> => {
  return Object.entries(properties).map(([key, value]) => ({
    type: "mdxJsxAttribute" as const,
    name: key,
    value:
      key === "cols"
        ? {
            type: "mdxJsxAttributeValueExpression" as const,
            value: String(value),
          }
        : String(value),
  }));
};

// ===== ENHANCED CONTENT PROCESSOR =====

class EnhancedContentProcessor {
  /**
   * Processa conteúdo HTML complexo que pode conter componentes aninhados e code blocks
   */
  static processComplexContent(htmlContent: string): HastNode[] {
    if (!htmlContent?.trim()) return [];

    const decoded = decodeHtmlEntities(
      htmlContent.replace(/\\"/g, '"').replace(/\\n/g, "\n")
    );

    try {
      const tree = unified()
        .use(rehypeParse, { fragment: true })
        .parse(decoded);

      // Clean up excessive paragraphs before processing
      this.cleanupExcessiveParagraphs(tree);
      
      // Processa a árvore recursivamente para transformar componentes aninhados
      this.transformNestedComponents(tree);

      return tree.children;
    } catch {
      return [{ type: "text", value: decoded } as HastText];
    }
  }

  /**
   * Remove parágrafos excessivos que podem ter sido criados durante conversões
   */
  private static cleanupExcessiveParagraphs(tree: HastRoot): void {
    visit(tree, "element", (node: HastElement, index, parent) => {
      if (!parent || typeof index !== "number") return;
      
      // Remove empty paragraphs
      if (node.tagName === "p" && 
          (node.children.length === 0 || 
           node.children.every(child => child.type === "text" && !child.value.trim()))) {
        if ("children" in parent) {
          parent.children.splice(index, 1);
          return ["skip", index];
        }
      }
      
      // Remove consecutive empty paragraphs
      if (node.tagName === "p" && index > 0 && "children" in parent) {
        const prevSibling = parent.children[index - 1];
        if (prevSibling?.type === "element" && 
            prevSibling.tagName === "p" && 
            (prevSibling.children.length === 0 || 
             prevSibling.children.every(child => child.type === "text" && !child.value.trim()))) {
          parent.children.splice(index - 1, 1);
          return ["skip", index - 1];
        }
      }
    });
  }

  /**
   * Transforma componentes aninhados recursivamente
   */
  private static transformNestedComponents(tree: HastRoot): void {
    visit(tree, "element", (node: HastElement) => {
      // Detecta componentes por data-type ou tag name
      const dataType = node.properties?.["dataType"];
      const isComponent =
        COMPONENT_TAGS.has(node.tagName) ||
        (dataType && COMPONENT_TAGS.has(String(dataType)));

      if (isComponent) {
        // Aplica transformação específica do componente
        this.applyComponentTransformation(
          node,
          dataType ? String(dataType) : node.tagName
        );
      }

      // Processa code blocks especiais
      if (
        node.tagName === "pre" &&
        node.properties?.["dataType"] === "codeBlock"
      ) {
        this.transformCodeBlock(node);
      }
    });
  }

  /**
   * Aplica transformação específica para cada tipo de componente
   */
  private static applyComponentTransformation(
    node: HastElement,
    componentType: string
  ): void {
    switch (componentType) {
      case "card":
        ComponentTransformers.card(node);
        break;
      case "cardlist":
        ComponentTransformers.cardList(node);
        break;
      case "accordion":
        ComponentTransformers.accordion(node);
        break;
      case "accordion-group":
        ComponentTransformers.accordionGroup(node);
        break;
      case "callout":
        ComponentTransformers.callout(node);
        break;
      case "steps":
        ComponentTransformers.steps(node);
        break;
      case "tabs":
        ComponentTransformers.tabs(node);
        break;
      case "image":
        ComponentTransformers.image(node);
        break;
      case "video":
        ComponentTransformers.video(node);
        break;
    }
  }

  /**
   * Transforma code blocks preservando syntax highlighting
   */
  private static transformCodeBlock(node: HastElement): void {
    const codeElement = node.children.find(
      (child) => child.type === "element" && child.tagName === "code"
    ) as HastElement | undefined;

    if (codeElement) {
      const language =
        node.properties?.["dataLanguage"] ||
        String(codeElement.properties?.className || "").replace(
          "language-",
          ""
        ) ||
        "text";

      // Preserva a estrutura para o processor principal
      node.properties = {
        ...node.properties,
        dataType: "codeBlock",
        dataLanguage: language,
      };
    }
  }

  /**
   * Processa descrição simples (sem componentes aninhados)
   */
  static processSimpleDescription(htmlContent: string): string {
    if (!htmlContent?.trim()) return "";

    const decoded = decodeHtmlEntities(htmlContent);

    if (!decoded.includes("<")) {
      return decoded.trim();
    }

    try {
      const tree = unified()
        .use(rehypeParse, { fragment: true })
        .parse(decoded);

      return this.extractTextContent(tree.children);
    } catch {
      return decoded.replace(/<[^>]+>/g, "").trim();
    }
  }

  /**
   * Processa conteúdo de Card sem adicionar parágrafos desnecessários
   */
  static processCardContent(htmlContent: string): HastNode[] {
    if (!htmlContent?.trim()) return [];

    const decoded = decodeHtmlEntities(
      htmlContent.replace(/\\"/g, '"').replace(/\\n/g, "\n")
    );

    try {
      const tree = unified()
        .use(rehypeParse, { fragment: true })
        .parse(decoded);

      // Primeiro, limpa parágrafos excessivos
      this.cleanupExcessiveParagraphs(tree);
      
      // Para Cards, detecta e corrige parágrafos duplos aninhados
      this.fixNestedParagraphs(tree);
      
      // Para Cards, se há apenas um parágrafo com class="rich-text-paragraph", 
      // extraia seu conteúdo diretamente para evitar <p><p></p></p>
      if (tree.children.length === 1 && 
          tree.children[0].type === "element" && 
          tree.children[0].tagName === "p") {
        const paragraph = tree.children[0] as HastElement;
        
        // Se é um parágrafo rich-text, extraia o conteúdo
        if (paragraph.properties?.class === "rich-text-paragraph" ||
            String(paragraph.properties?.className || "").includes("rich-text-paragraph")) {
          return paragraph.children;
        }
        
        // Se é um parágrafo simples sem conteúdo complexo, extraia o conteúdo
        const hasComplexContent = paragraph.children.some(child => 
          child.type === "element" && !FORMATTING_TAGS.has(child.tagName) && child.tagName !== "br" && child.tagName !== "code"
        );
        
        if (!hasComplexContent) {
          return paragraph.children;
        }
      }
      
      // Processa componentes aninhados
      this.transformNestedComponents(tree);

      return tree.children;
    } catch {
      return [{ type: "text", value: decoded } as HastText];
    }
  }

  /**
   * Corrige parágrafos duplos aninhados como <p><p>...</p></p>
   */
  private static fixNestedParagraphs(tree: HastRoot): void {
    visit(tree, "element", (node: HastElement, index, parent) => {
      if (!parent || typeof index !== "number") return;
      
      // Detecta <p><p class="rich-text-paragraph">...</p></p>
      if (node.tagName === "p" && 
          node.children.length === 1 && 
          node.children[0].type === "element" && 
          node.children[0].tagName === "p") {
        
        const innerParagraph = node.children[0] as HastElement;
        
        // Se o parágrafo interno tem class="rich-text-paragraph", promova-o
        if (innerParagraph.properties?.class === "rich-text-paragraph" ||
            String(innerParagraph.properties?.className || "").includes("rich-text-paragraph")) {
          if ("children" in parent) {
            parent.children.splice(index, 1, innerParagraph);
            return ["skip", index];
          }
        }
      }
    });
  }

  private static extractTextContent(
    children: (RootContent | ElementContent)[]
  ): string {
    const parts: string[] = [];

    for (const child of children) {
      if (child.type === "text") {
        parts.push(child.value);
      } else if (child.type === "element") {
        switch (child.tagName) {
          case "p":
            const content = this.extractTextContent(child.children);
            if (content.trim()) {
              // Add paragraph spacing only if there are already parts
              if (parts.length > 0) {
                parts.push("\n\n" + content);
              } else {
                parts.push(content);
              }
            }
            break;
          case "br":
            parts.push("\n");
            break;
          case "code":
            const code = this.extractTextContent(child.children);
            parts.push(`\`${code}\``);
            break;
          default:
            if (FORMATTING_TAGS.has(child.tagName)) {
              parts.push(toHtml(child));
            } else {
              parts.push(this.extractTextContent(child.children));
            }
        }
      }
    }

    return parts.join("").replace(/\n{3,}/g, "\n\n").trim();
  }
}

// ===== ENHANCED COMPONENT TRANSFORMERS =====

class ComponentTransformers {
  static metadata(
    node: HastElement,
    context: TransformContext,
    parent: HastParent,
    index: number
  ): void {
    const { title, description, slug, ...otherProps } = node.properties || {};

    context.frontmatter.title = String(title || "");
    context.frontmatter.description = String(description || "");
    context.frontmatter.slug = String(slug || "");

    Object.entries(otherProps).forEach(([key, value]) => {
      if (!key.startsWith("data") && key !== "dataType" && value != null) {
        context.frontmatter[key] = String(value);
      }
    });

    if ("children" in parent) {
      parent.children.splice(index, 1);
    }
  }

  static card(node: HastElement): void {
    const props = node.properties || {};
    const newProps: Properties = {};

    Object.entries(props).forEach(([key, value]) => {
      const cleanKey = key.replace(/^data-?/, "").toLowerCase();
      if (cleanKey !== "component") {
        const camelKey = cleanKey.replace(/-([a-z])/g, (_, letter) =>
          letter.toUpperCase()
        );
        newProps[camelKey] = String(value);
      }
    });

    // Processa conteúdo de Card evitando parágrafos extras
    if (props.description) {
      const cardContent = EnhancedContentProcessor.processCardContent(
        String(props.description)
      );
      node.children = cardContent as HastElement["children"];
    } else if (node.children.length > 0) {
      const childrenHtml = toHtml(node.children);
      if (childrenHtml.trim()) {
        const cardContent =
          EnhancedContentProcessor.processCardContent(childrenHtml);
        node.children = cardContent as HastElement["children"];
      }
    }

    node.properties = newProps;
    node.tagName = "Card";
  }

  static cardList(node: HastElement): void {
    node.tagName = "CardList";

    const cols = node.properties?.cols || node.properties?.["data-cols"];
    node.properties = cols ? { cols: String(cols) } : {};

    // Transforma child cards recursivamente
    visit(node, "element", (child: HastElement) => {
      if (
        child.tagName === "card" ||
        child.properties?.["dataType"] === "card"
      ) {
        this.card(child);
      }
    });
  }

  static accordion(node: HastElement): void {
    const props = node.properties || {};
    const newProps: Properties = {};

    if (props.title) {
      newProps.title = String(props.title);
    }

    // Processa conteúdo complexo com componentes aninhados
    if (props.description) {
      const complexContent = EnhancedContentProcessor.processComplexContent(
        String(props.description)
      );
      node.children = complexContent as HastElement["children"];
    } else if (node.children.length > 0) {
      // Processa children existentes para detectar componentes aninhados
      EnhancedContentProcessor.processComplexContent(toHtml(node.children));
    }

    node.properties = newProps;
    node.tagName = "Accordion";
  }

  static accordionGroup(node: HastElement): void {
    node.tagName = "AccordionGroup";
    node.properties = {};

    const dataAccordions = node.properties?.["dataAccordions"];
    if (dataAccordions && typeof dataAccordions === "string") {
      try {
        const accordions = JSON.parse(dataAccordions);
        if (Array.isArray(accordions)) {
          node.children = accordions.map((accordion) => {
            const accordionNode: HastElement = {
              type: "element",
              tagName: "Accordion",
              properties: { title: accordion.title || "" },
              children: [],
            };

            if (accordion.description) {
              const complexContent =
                EnhancedContentProcessor.processComplexContent(
                  accordion.description
                );
              accordionNode.children =
                complexContent as HastElement["children"];
            }

            return accordionNode;
          });
        }
      } catch (error) {
        console.warn("Failed to parse accordion data:", error);
      }
    }
  }

  static callout(node: HastElement): void {
    const props = node.properties || {};
    const newProps: Properties = {};

    Object.entries(props).forEach(([key, value]) => {
      if (key === "type" && value !== "callout") {
        newProps.type = String(value);
      } else if (key === "title") {
        newProps.title = String(value);
      }
    });

    // Processa conteúdo complexo
    if (props.description) {
      const complexContent = EnhancedContentProcessor.processComplexContent(
        String(props.description)
      );
      node.children = complexContent as HastElement["children"];
    } else if (node.children.length > 0) {
      const childrenHtml = toHtml(node.children);
      if (childrenHtml.trim()) {
        const complexContent =
          EnhancedContentProcessor.processComplexContent(childrenHtml);
        node.children = complexContent as HastElement["children"];
      }
    }

    node.properties = newProps;
    node.tagName = "Callout";
  }

  static steps(node: HastElement): void {
    const props = node.properties || {};

    if (props["dataSteps"]) {
      try {
        const stepsData = JSON.parse(String(props["dataSteps"]));
        if (Array.isArray(stepsData)) {
          node.children = this.processStepsRecursively(stepsData);
        }
      } catch (error) {
        console.warn("Error parsing steps data:", error);
      }
    }

    node.properties = {};
    node.tagName = "Steps";
  }

  private static processStepsRecursively(steps: StepData[]): HastElement[] {
    const elements: HastElement[] = [];

    for (const step of steps) {
      const titleSize = step.titleSize || "h3";

      // Add title
      elements.push({
        type: "element",
        tagName: titleSize,
        properties: {},
        children: [{ type: "text", value: step.title || "" }],
      });

      // Processa conteúdo complexo que pode conter componentes
      if (step.content) {
        const complexContent = EnhancedContentProcessor.processComplexContent(
          step.content
        );
        elements.push(
          ...complexContent.filter(
            (node): node is HastElement => node.type === "element"
          )
        );
      }

      // Add sub-steps recursivamente
      if (step.subSteps && step.subSteps.length > 0) {
        const subStepTitleSize = step.subSteps[0]?.titleSize || "h4";
        elements.push({
          type: "element",
          tagName: "Steps",
          properties: { titleSize: subStepTitleSize },
          children: this.processStepsRecursively(step.subSteps),
        });
      }
    }

    return elements;
  }

  static tabs(node: HastElement): void {
    const props = node.properties || {};

    if (props["dataTabs"]) {
      try {
        const tabsData = JSON.parse(String(props["dataTabs"]));
        if (Array.isArray(tabsData)) {
          node.children = tabsData.map((tab) => {
            const tabElement: HastElement = {
              type: "element",
              tagName: "Tab",
              properties: {
                value: tab.value || "",
                ...(tab.label &&
                  tab.label !== tab.value && { label: tab.label }),
                ...(tab.default && { default: "true" }),
              },
              children: [],
            };

            // Processa conteúdo complexo do tab
            if (tab.content) {
              const complexContent =
                EnhancedContentProcessor.processComplexContent(tab.content);
              tabElement.children = complexContent as HastElement["children"];
            }

            return tabElement;
          });
        }
      } catch (error) {
        console.warn("Error parsing tabs data:", error);
      }
    }

    node.properties = {};
    node.tagName = "Tabs";
  }

  static image(node: HastElement): void {
    const props = node.properties || {};
    const newProps: Properties = {};

    const imgElement = node.children.find(
      (child) => child.type === "element" && child.tagName === "img"
    ) as HastElement | undefined;

    if (imgElement?.properties) {
      const imgProps = imgElement.properties;
      if (imgProps.src) newProps.src = String(imgProps.src);
      if (imgProps["dataSrcDark"])
        newProps.srcDark = String(imgProps["dataSrcDark"]);
      if (imgProps.alt) newProps.alt = String(imgProps.alt);
    }

    if (props["dataSize"]) {
      newProps.size = String(props["dataSize"]);
    }

    node.properties = newProps;
    node.tagName = "Image";
    node.children = [];
  }

  static video(node: HastElement): void {
    const props = node.properties || {};
    const newProps: Properties = {};

    const iframeElement = node.children.find(
      (child) => child.type === "element" && child.tagName === "iframe"
    ) as HastElement | undefined;

    if (iframeElement?.properties) {
      const iframeProps = iframeElement.properties;
      if (iframeProps.src) newProps.src = String(iframeProps.src);
      if (iframeProps.title) newProps.alt = String(iframeProps.title);
    }

    if (props["dataWidth"]) {
      newProps.width = String(props["dataWidth"]);
    }

    node.properties = newProps;
    node.tagName = "Video";
    node.children = [];
  }
}

// ===== MAIN TRANSFORM PLUGIN =====

const enhancedTransformPlugin = () => {
  return (tree: HastRoot, file: VFile) => {
    const context: TransformContext = {
      frontmatter: {},
      componentMap: new Map(),
    };

    visit(tree, "element", (node, index, parent) => {
      if (!parent || typeof index !== "number") return;

      switch (node.tagName) {
        case "metadata":
          ComponentTransformers.metadata(node, context, parent, index);
          return ["skip", index];

        case "head":
          if (node.properties?.["dataType"] === "metadata") {
            visit(node, "element", (child) => {
              if (
                child.tagName === "meta" &&
                child.properties?.name &&
                child.properties?.content
              ) {
                context.frontmatter[String(child.properties.name)] = String(
                  child.properties.content
                );
              }
            });
            if ("children" in parent) {
              parent.children.splice(index, 1);
            }
            return ["skip", index];
          }
          break;

        case "card":
          ComponentTransformers.card(node);
          break;

        case "cardlist":
          ComponentTransformers.cardList(node);
          break;

        case "div":
          const dataType = node.properties?.["dataType"];
          switch (dataType) {
            case "accordion":
              ComponentTransformers.accordion(node);
              break;
            case "accordion-group":
              ComponentTransformers.accordionGroup(node);
              break;
            case "callout":
              ComponentTransformers.callout(node);
              break;
            case "steps":
              ComponentTransformers.steps(node);
              break;
            case "tabs":
              ComponentTransformers.tabs(node);
              break;
            case "image":
              ComponentTransformers.image(node);
              break;
            case "video":
              ComponentTransformers.video(node);
              break;
          }
          break;

        case "p":
          if (isEmptyNode(node)) {
            // Remove empty paragraphs instead of converting to br
            if ("children" in parent) {
              parent.children.splice(index, 1);
            }
            return ["skip", index];
          }
          break;

        case "table":
          visit(node, "element", (el, elIndex, elParent) => {
            if (
              ["table", "tbody", "thead", "tfoot", "tr", "th", "td"].includes(
                el.tagName
              )
            ) {
              el.properties = {};
            }

            if (
              el.tagName === "colgroup" &&
              elParent &&
              typeof elIndex === "number"
            ) {
              elParent.children.splice(elIndex, 1);
              return ["skip", elIndex];
            }

            if (el.tagName === "th" || el.tagName === "td") {
              const newChildren: HastElement["children"] = [];
              el.children.forEach((child) => {
                if (child.type === "element" && child.tagName === "p") {
                  if (isEmptyNode(child)) {
                    // Skip empty paragraphs in table cells
                    return;
                  } else {
                    newChildren.push(...child.children);
                  }
                } else {
                  newChildren.push(child);
                }
              });
              el.children = newChildren;
            }
          });
          break;
      }
    });

    if (Object.keys(context.frontmatter).length > 0) {
      file.data.frontmatter = context.frontmatter;
    }
  };
};

// ===== EXPORT =====

export const htmlToMdx = async (html: string): Promise<string> => {
  const validation = validateHtmlForMdx(html);
  if (!validation.isValid) {
    throw new Error(`Invalid HTML input: ${validation.errors.join(", ")}`);
  }

  if (validation.warnings.length > 0) {
    console.warn("⚠️ Conversion warnings:", validation.warnings);
  }

  const rehypeRemarkOptions: RehypeRemarkOptions = {
    handlers: {
      // Preserve formatting tags as HTML
      ...Object.fromEntries(
        Array.from(FORMATTING_TAGS).map((tag) => [
          tag,
          (_h: unknown, node: HastElement) => ({
            type: "html",
            value: toHtml(node),
          }),
        ])
      ),

      // Handle tables
      table: (_h: unknown, node: HastElement) => {
        visit(node, "element", (el, index, parent) => {
          if (
            el.tagName === "colgroup" &&
            parent &&
            typeof index === "number"
          ) {
            parent.children.splice(index, 1);
            return ["skip", index];
          }
        });
        return { type: "html", value: toHtml(node) };
      },

      // Handle breaks
      br: () => ({ type: "html", value: "<br />" }),

      // Handle code blocks
      pre: (_h: unknown, node: HastElement) => {
        const codeElement = node.children.find(
          (child) => child.type === "element" && child.tagName === "code"
        ) as HastElement | undefined;

        if (codeElement && node.properties?.["dataType"] === "codeBlock") {
          const language =
            node.properties["dataLanguage"] ||
            (codeElement.properties?.className as string)?.replace(
              "language-",
              ""
            ) ||
            "text";

          const extractText = (el: HastElement): string => {
            return el.children
              .map((child) => {
                if (child.type === "text") return child.value;
                if (child.type === "element") return extractText(child);
                return "";
              })
              .join("");
          };

          return {
            type: "code",
            lang: String(language),
            value: extractText(codeElement),
          };
        }

        return { type: "html", value: toHtml(node) };
      },

      // MDX Components
      ...Object.fromEntries(
        [
          "Card",
          "CardList",
          "Accordion",
          "AccordionGroup",
          "Callout",
          "Steps",
          "Tabs",
          "Tab",
          "Image",
          "Video",
        ].map((componentName) => [
          componentName,
          (h: { all: (node: HastElement) => unknown[] }, node: HastElement) => {
            const attributes = createJsxAttributes(node.properties);
            const children = h.all(node) as (
              | BlockContent
              | DefinitionContent
            )[];

            return {
              type: "mdxJsxFlowElement" as const,
              name: componentName,
              attributes,
              children: componentName === "Image" ? [] : children,
            };
          },
        ])
      ),
    },
  };

  const addFrontmatterPlugin = () => (tree: MdastRoot, file: VFile) => {
    const frontmatter = file.data.frontmatter as Record<string, string>;
    if (frontmatter && Object.keys(frontmatter).length > 0) {
      tree.children.unshift({
        type: "yaml",
        value: Object.entries(frontmatter)
          .map(([key, value]) => `${key}: "${value}"`)
          .join("\n"),
      });
    }
  };

  const file = await unified()
    .use(rehypeParse, { fragment: true })
    .use(enhancedTransformPlugin)
    .use(rehypeRemark, rehypeRemarkOptions)
    .use(remarkFrontmatter, ["yaml"])
    .use(remarkMdx)
    .use(remarkGfm)
    .use(remarkStringify, {
      bullet: "-",
      fence: "`",
      fences: true,
      incrementListMarker: false,
      emphasis: "*",
      strong: "*",
      rule: "-",
      ruleRepetition: 3,
      ruleSpaces: false,
      listItemIndent: "mixed",
      join: [
        (left, right) =>
          left.type === "mdxJsxFlowElement" ||
          right.type === "mdxJsxFlowElement"
            ? 1
            : null,
      ],
    })
    .use(addFrontmatterPlugin)
    .process(html);

  let mdx = String(file);

  // Enhanced cleanup to prevent excessive paragraph tags and line breaks
  mdx = mdx
    .replace(/&#x20;/g, " ")
    .replace(/&#x([0-9A-Fa-f]+);/g, (match, hex) => {
      const code = parseInt(hex, 16);
      return code === 32 ||
        (code >= 65 && code <= 90) ||
        (code >= 97 && code <= 122)
        ? String.fromCharCode(code)
        : match;
    })
    .replace(/\\([<>*\[\]])/g, "$1")
    .replace(/[ \t]+\n/g, "\n")
    .replace(/\n{4,}/g, "\n\n\n")
    // Remove excessive empty paragraphs that might be generated
    .replace(/<p>\s*<\/p>/g, "")
    .replace(/\n\s*\n\s*\n/g, "\n\n")
    // Fix double paragraph patterns in Card content 
    .replace(/<p class="rich-text-paragraph"><p class="rich-text-paragraph">/g, '<p class="rich-text-paragraph">')
    .replace(/<\/p><\/p>/g, '</p>')
    // Clean up spaces around components
    .replace(/\n\s*(<[A-Z][^>]*>)/g, "\n\n$1")
    .replace(/(<\/[A-Z][^>]*>)\s*\n/g, "$1\n\n");

  // Normalize self-closing tags
  Array.from(SELF_CLOSING_TAGS).forEach(tag => {
    const pattern = new RegExp(`<${tag}(\\s[^>]*)?(?<!/)>`, "gi");
    mdx = mdx.replace(pattern, (_match, attrs) =>
      attrs ? `<${tag}${attrs} />` : `<${tag} />`
    );
  });
  
  // Final cleanup to ensure proper spacing
  mdx = mdx
    .replace(/\n{3,}/g, "\n\n")
    .replace(/^\n+/, "")
    .replace(/\n+$/, "\n");

  console.log(mdx);
  return mdx;
};
