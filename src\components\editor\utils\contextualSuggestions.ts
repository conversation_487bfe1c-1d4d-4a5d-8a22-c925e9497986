import type { Editor } from "@tiptap/react";
import type { CommandProps } from "../types";

// Function to get context-aware suggestions
export const getContextualSuggestions = (editor: Editor | null) => {
  // Return basic suggestions if editor is not ready
  if (!editor) {
    return [
      {
        title: "Text",
        searchTerms: ["paragraph", "p", "text"],
        command: () => {},
      },
      {
        title: "Heading 1",
        searchTerms: ["h1", "heading", "title", "big"],
        command: () => {},
      },
      {
        title: "Table",
        searchTerms: ["table", "grid", "data", "rows", "columns"],
        command: () => {},
      },
    ];
  }

  const { selection } = editor.state;
  const { $from } = selection;

  // Check current context - simplified approach
  let isInTable = false;
  let currentNode = $from.node();
  let depth = $from.depth;

  // Walk up the node tree to find if we're in a table
  while (depth >= 0) {
    if (currentNode && currentNode.type.name === "table") {
      isInTable = true;
      break;
    }
    if (depth > 0) {
      currentNode = $from.node(depth - 1);
    }
    depth--;
  }

  const isInCodeBlock = $from.parent.type.name === "codeBlock";

  // Check if metadata node already exists
  let hasMetadataNode = false;
  editor.state.doc.descendants((node) => {
    if (node.type.name === "metadataNode") {
      hasMetadataNode = true;
      return false;
    }
  });

  const allSuggestions = [
    // Metadata (only if not exists)
    ...(hasMetadataNode
      ? []
      : [
          {
            title: "Metadata",
            searchTerms: ["metadata", "meta", "seo", "title", "description"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).run();
              editor.commands.insertContentAt(0, {
                type: "metadataNode",
                attrs: { title: "", description: "", slug: "" },
              });
            },
          },
        ]),

    // Basic content (always available)
    {
      title: "Text",
      searchTerms: ["paragraph", "p", "text"],
      command: ({ editor, range }: CommandProps) => {
        // First clear the slash command
        editor.chain().focus().deleteRange(range).run();

        // Then clear all formatting and set to paragraph
        editor
          .chain()
          .focus()
          .clearNodes()
          .setParagraph()
          .unsetAllMarks()
          .run();

        // Force cursor to have no active marks for future typing
        const { state, dispatch } = editor.view;
        const { tr } = state;
        tr.setStoredMarks([]);
        dispatch(tr);
      },
    },

    // Headings (not in code blocks)
    ...(isInCodeBlock
      ? []
      : [
          {
            title: "Heading 1",
            searchTerms: ["h1", "heading", "title", "big"],
            command: ({ editor, range }: CommandProps) => {
              editor
                .chain()
                .focus()
                .deleteRange(range)
                .setNode("heading", { level: 1 })
                .run();
            },
          },
          {
            title: "Heading 2",
            searchTerms: ["h2", "heading", "subtitle"],
            command: ({ editor, range }: CommandProps) => {
              editor
                .chain()
                .focus()
                .deleteRange(range)
                .setNode("heading", { level: 2 })
                .run();
            },
          },
          {
            title: "Heading 3",
            searchTerms: ["h3", "heading", "subtitle"],
            command: ({ editor, range }: CommandProps) => {
              editor
                .chain()
                .focus()
                .deleteRange(range)
                .setNode("heading", { level: 3 })
                .run();
            },
          },
        ]),

    // Lists (not in code blocks)
    ...(isInCodeBlock
      ? []
      : [
          {
            title: "Bullet List",
            searchTerms: ["ul", "bullet list", "unordered", "list"],
            command: ({ editor, range }: CommandProps) => {
              editor
                .chain()
                .focus()
                .deleteRange(range)
                .toggleBulletList()
                .run();
            },
          },
          {
            title: "Numbered List",
            searchTerms: ["ol", "ordered list", "numbered", "list"],
            command: ({ editor, range }: CommandProps) => {
              editor
                .chain()
                .focus()
                .deleteRange(range)
                .toggleOrderedList()
                .run();
            },
          },
        ]),

    // Table commands - only show table creation if NOT in table
    ...(isInTable
      ? []
      : [
          {
            title: "Table",
            searchTerms: ["table", "grid", "data", "rows", "columns"],
            command: ({ editor, range }: CommandProps) => {
              editor
                .chain()
                .focus()
                .deleteRange(range)
                .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
                .run();
            },
          },
        ]),

    // Table manipulation - only show if IN table
    ...(isInTable
      ? [
          {
            title: "Add Column",
            searchTerms: ["column", "add column", "table column"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).addColumnAfter().run();
            },
          },
          {
            title: "Add Row",
            searchTerms: ["row", "add row", "table row"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).addRowAfter().run();
            },
          },
          {
            title: "Remove Column",
            searchTerms: ["remove column", "delete column", "column delete"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).deleteColumn().run();
            },
          },
          {
            title: "Remove Row",
            searchTerms: ["remove row", "delete row", "row delete"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).deleteRow().run();
            },
          },
          {
            title: "Delete Table",
            searchTerms: ["delete table", "remove table"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).deleteTable().run();
            },
          },
        ]
      : []),

    // Text formatting (not in code blocks)
    ...(isInCodeBlock
      ? []
      : [
          {
            title: "Bold",
            searchTerms: ["bold", "strong", "b"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).toggleBold().run();
            },
          },
          {
            title: "Italic",
            searchTerms: ["italic", "emphasize", "i"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).toggleItalic().run();
            },
          },
          {
            title: "Link",
            searchTerms: ["link", "url", "href", "anchor"],
            command: ({ editor, range }: CommandProps) => {
              editor.chain().focus().deleteRange(range).run();

              // Trigger link dialog event
              const event = new CustomEvent("openLinkDialog", {
                detail: { editor },
              });
              window.dispatchEvent(event);
            },
          },
        ]),

    // Code block (always available)
    {
      title: "Code Block",
      searchTerms: ["code", "block", "pre"],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "codeBlock",
            attrs: {
              language: "javascript",
              code: "",
            },
          })
          .run();
      },
    },

    // Card (always available)
    {
      title: "Card",
      searchTerms: ["card", "link", "component"],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "cardNode",
            attrs: {
              title: "Card Title",
              description: "Card description",
              link: "",
              icon: "",
              iconType: "regular",
              iconSize: "48px",
              image: "",
            },
          })
          .run();
      },
    },

    // Card List (always available)
    {
      title: "Card List",
      searchTerms: ["cardlist", "cards", "grid", "group"],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "cardListNode",
            attrs: {
              cols: 2,
              cards: [
                {
                  title: "Card Title",
                  description: "Card description",
                  link: "",
                  icon: "",
                  iconType: "regular",
                  iconSize: "48px",
                  image: "",
                },
              ],
            },
          })
          .run();
      },
    },

    // Callout (always available)
    {
      title: "Callout",
      searchTerms: [
        "callout",
        "alert",
        "note",
        "info",
        "warning",
        "tip",
        "notice",
      ],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "callout",
            attrs: {
              type: "info",
              title: "Info",
              description: "This adds an Info Callout to the content.",
            },
          })
          .run();
      },
    },

    // Accordion (always available)
    {
      title: "Accordion",
      searchTerms: ["accordion", "collapse", "expand", "dropdown"],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "accordion",
            attrs: {
              title: "Code for an Accordion",
              description: "Description for an Accordion.",
            },
          })
          .run();
      },
    },

    // Accordion Group (always available)
    {
      title: "Accordion Group",
      searchTerms: ["accordiongroup", "accordions", "group", "multiple"],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "accordionGroup",
            attrs: {
              accordions: [
                {
                  id: "1",
                  title: "I'm an Accordion",
                  description:
                    "You can add any content in here. Check out Accordion Group if you want to group multiple Accordions into a single display.",
                  isExpanded: false,
                },
              ],
            },
          })
          .run();
      },
    },

    // Steps (always available)
    {
      title: "Steps",
      searchTerms: [
        "steps",
        "guide",
        "tutorial",
        "process",
        "sequence",
        "numbered",
      ],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "steps",
            attrs: {
              titleSize: "h3",
              steps: [
                {
                  id: "1",
                  title: "First Step",
                  content: "<p>First step content</p>",
                },
                {
                  id: "2",
                  title: "Second Step",
                  content: "<p>Second step content</p>",
                },
              ],
            },
          })
          .run();
      },
    },

    // Tabs (always available)
    {
      title: "Tabs",
      searchTerms: [
        "tabs",
        "tabbed",
        "navigation",
        "switch",
        "toggle",
        "sections",
      ],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "tabs",
            attrs: {
              tabs: [
                {
                  id: "1",
                  value: "tab-1",
                  label: "Tab 1",
                  content: "<p>Content for Tab 1</p>",
                  default: true,
                },
                {
                  id: "2",
                  value: "tab-2",
                  label: "Tab 2",
                  content: "<p>Content for Tab 2</p>",
                  default: false,
                },
                {
                  id: "3",
                  value: "tab-3",
                  label: "Tab 3",
                  content: "<p>Content for Tab 3</p>",
                  default: false,
                },
              ],
            },
          })
          .run();
      },
    },

    // Image (always available)
    {
      title: "Image",
      searchTerms: ["image", "picture", "photo", "media", "img"],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "imageNode",
            attrs: {
              src: "",
              srcDark: "",
              size: "50%",
              alt: "",
            },
          })
          .run();
      },
    },

    // Video (always available)
    {
      title: "Video",
      searchTerms: ["video", "youtube", "embed", "media", "iframe"],
      command: ({ editor, range }: CommandProps) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: "videoNode",
            attrs: {
              src: "",
              width: "500px",
              alt: "",
            },
          })
          .run();
      },
    },

    // Blockquote (not in code blocks)
    ...(isInCodeBlock
      ? []
      : [
          {
            title: "Blockquote",
            searchTerms: ["quote", "blockquote", "cite"],
            command: ({ editor, range }: CommandProps) => {
              editor
                .chain()
                .focus()
                .deleteRange(range)
                .toggleBlockquote()
                .run();
            },
          },
        ]),

    // Horizontal Rule (not in tables or code blocks)
    ...(isInTable || isInCodeBlock
      ? []
      : [
          {
            title: "Horizontal Rule",
            searchTerms: ["hr", "line", "divider", "horizontal rule"],
            command: ({ editor, range }: CommandProps) => {
              editor
                .chain()
                .focus()
                .deleteRange(range)
                .setHorizontalRule()
                .run();
            },
          },
        ]),
  ];

  return allSuggestions;
};
