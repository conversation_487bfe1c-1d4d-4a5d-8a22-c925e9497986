"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Upload,
  X,
  ImageIcon,
  Filter,
  Trash2,
  Loader2,
} from "lucide-react";
import { type OrganizationImage } from "@/hooks/useOrganizationImages";
import { createClient } from "@/utils/supabase/client";

interface ImageSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectImage: (imageUrl: string) => void;
  projectId?: number;
  organizationId?: string;
}

interface PendingImage {
  id: string;
  file: File;
  preview: string;
  name: string;
  customName: string;
  size: number;
  altText: string;
  tags: string[];
}

export const ImageSelector: React.FC<ImageSelectorProps> = ({
  isOpen,
  onClose,
  onSelectImage,
  projectId,
  organizationId,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showUpload, setShowUpload] = useState(false);
  const [pendingImages, setPendingImages] = useState<PendingImage[]>([]);
  const [uploading, setUploading] = useState(false);
  const [images, setImages] = useState<OrganizationImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fixed limits for all users
  const FIXED_LIMITS = {
    max_images: 50,
    max_size_mb_per_image: 10,
    image_compression_quality: 60,
  };

  // Load images function (removed limits loading)
  const loadImages = async () => {
    if (!projectId || !organizationId) return;

    setLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      console.log("Loading images for:", { projectId, organizationId });

      const imagesResult = await supabase
        .from("organization_images")
        .select("*")
        .eq("project_id", projectId)
        .eq("organization_id", organizationId)
        .order("created_at", { ascending: false });

      if (imagesResult.error) {
        console.error("Error loading images:", imagesResult.error);
        setError(imagesResult.error.message);
        return;
      }

      console.log("Images loaded:", imagesResult.data);
      setImages(imagesResult.data || []);
    } catch (err) {
      console.error("Loading error:", err);
      setError(err instanceof Error ? err.message : "Failed to load images");
    } finally {
      setLoading(false);
    }
  };

  // Load images when the modal opens
  React.useEffect(() => {
    if (isOpen && projectId && organizationId) {
      loadImages();
    }
  }, [isOpen, projectId, organizationId]);

  // Debug logging
  console.log("ImageSelector - Debug info:", {
    isOpen,
    projectId,
    organizationId,
    imagesCount: images.length,
    loading,
    error,
  });

  // Filter images based on search and tags
  const filteredImages = images.filter((image: OrganizationImage) => {
    const matchesSearch =
      image.image_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (image.alt_text &&
        image.alt_text.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesTags =
      selectedTags.length === 0 ||
      (image.tags && selectedTags.some((tag) => image.tags?.includes(tag)));

    return matchesSearch && matchesTags;
  });

  // Get unique tags from all images
  const availableTags = Array.from(
    new Set(images.flatMap((image: OrganizationImage) => image.tags || []))
  ).sort();

  const handleImageSelect = (image: (typeof images)[0]) => {
    onSelectImage(image.image_url);
    onClose();
  };

  const handleDeleteImage = async (
    image: OrganizationImage,
    event: React.MouseEvent
  ) => {
    event.stopPropagation(); // Prevent triggering handleImageSelect

    if (
      !confirm(
        `Are you sure you want to delete "${image.image_name}"? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      const supabase = createClient();

      // Delete from storage bucket
      const { error: storageError } = await supabase.storage
        .from("organization-images")
        .remove([image.image_path]);

      if (storageError) {
        console.error("Storage deletion error:", storageError);
        alert("Failed to delete image from storage: " + storageError.message);
        return;
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from("organization_images")
        .delete()
        .eq("id", image.id);

      if (dbError) {
        console.error("Database deletion error:", dbError);
        alert("Failed to delete image from database: " + dbError.message);
        return;
      }

      // Update local state
      setImages((prevImages) =>
        prevImages.filter((img) => img.id !== image.id)
      );

      console.log("Image deleted successfully:", image.image_name);
    } catch (error) {
      console.error("Error deleting image:", error);
      alert(
        "Failed to delete image: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Clear the input value to allow selecting the same file again
    event.target.value = "";

    const validFiles: File[] = [];
    const errors: string[] = [];

    // Check limits before processing
    const remainingSlots = FIXED_LIMITS.max_images - images.length;

    if (files.length > remainingSlots) {
      errors.push(
        `Image limit exceeded. You can only add ${remainingSlots} more image(s).`
      );
    }

    // Validate files first
    Array.from(files).forEach((file) => {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        errors.push(`${file.name}: Unsupported file type`);
        return;
      }

      // Validate file size based on project limits
      if (file.size > FIXED_LIMITS.max_size_mb_per_image * 1024 * 1024) {
        const maxMB = FIXED_LIMITS.max_size_mb_per_image;
        const fileMB = (file.size / (1024 * 1024)).toFixed(2);
        errors.push(
          `${file.name}: File too large (${fileMB}MB). Maximum allowed: ${maxMB}MB`
        );
        return;
      }

      // Check if already added
      if (
        pendingImages.some(
          (img) => img.name === file.name && img.size === file.size
        )
      ) {
        errors.push(`${file.name}: Already added`);
        return;
      }

      validFiles.push(file);
    });

    // Show errors if any
    if (errors.length > 0) {
      alert("Some files could not be added:\n" + errors.join("\n"));
    }

    // Process valid files
    if (validFiles.length === 0) return;

    let processedCount = 0;
    const newPendingImages: PendingImage[] = [];

    validFiles.forEach((file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const pendingImage: PendingImage = {
          id: Math.random().toString(36).substring(2, 15),
          file,
          preview: e.target?.result as string,
          name: file.name,
          customName: file.name.replace(/\.[^/.]+$/, ""), // Default to filename without extension
          size: file.size,
          altText: file.name.replace(/\.[^/.]+$/, ""), // Remove extension
          tags: [],
        };
        newPendingImages.push(pendingImage);
        processedCount++;

        // Update state when all valid files are processed
        if (processedCount === validFiles.length) {
          setPendingImages((prev) => [...prev, ...newPendingImages]);
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const handleRemovePendingImage = (id: string) => {
    setPendingImages((prev) => {
      const updated = prev.filter((img) => img.id !== id);
      // Revoke object URL to prevent memory leaks
      const imageToRemove = prev.find((img) => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return updated;
    });
  };

  const handleUpdatePendingImage = (
    id: string,
    field: keyof PendingImage,
    value: string | string[]
  ) => {
    setPendingImages((prev) =>
      prev.map((img) => (img.id === id ? { ...img, [field]: value } : img))
    );
  };

  const handleUpdateTags = (id: string, tagsString: string) => {
    const tags = tagsString
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag.length > 0);
    handleUpdatePendingImage(id, "tags", tags);
  };

  const handleBulkUpload = async () => {
    if (pendingImages.length === 0) {
      alert("Please select at least one image to upload");
      return;
    }

    if (!projectId || !organizationId) {
      alert("Error: Project or organization ID not found");
      return;
    }

    setUploading(true);

    try {
      // Get authenticated client
      const supabase = createClient();

      // Get current session
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session) {
        throw new Error("User not authenticated");
      }

      // Convert files to base64 for transmission
      const imagesData = await Promise.all(
        pendingImages.map(async (img) => {
          return new Promise<{
            name: string;
            file: string;
            contentType: string;
            altText: string;
            tags: string[];
            metadata: Record<string, unknown>;
          }>((resolve) => {
            const reader = new FileReader();
            reader.onload = () => {
              const result = reader.result as string;
              // Remove data:image/type;base64, prefix
              const base64 = result.split(",")[1];
              resolve({
                name: img.customName || img.name, // Use custom name if provided
                file: base64,
                contentType: img.file.type,
                altText: img.altText,
                tags: img.tags,
                metadata: {
                  originalSize: img.size,
                  originalName: img.name,
                  uploadedAt: new Date().toISOString(),
                },
              });
            };
            reader.readAsDataURL(img.file);
          });
        })
      );

      // Get Supabase URL from environment
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;

      console.log("=== DEBUG INFO ===");
      console.log("Supabase URL:", supabaseUrl);
      console.log("User authenticated:", !!session.user);
      console.log(
        "Session access token length:",
        session.access_token?.length || 0
      );
      console.log("Project ID:", projectId);
      console.log("Organization ID:", organizationId);
      console.log("Images data length:", imagesData.length);
      console.log(
        "First image sample:",
        imagesData[0]
          ? {
              name: imagesData[0].name,
              contentType: imagesData[0].contentType,
              fileSize: imagesData[0].file?.length || 0,
            }
          : "No images"
      );

      if (!supabaseUrl) {
        throw new Error(
          "Supabase URL not found - Check NEXT_PUBLIC_SUPABASE_URL environment variable"
        );
      }

      if (!session.access_token) {
        throw new Error(
          "No access token found - User authentication may have expired"
        );
      }

      // Call our Simplified Edge Function with user session token
      console.log("Chamando edge function com:", {
        projectId,
        organizationId,
        imageCount: imagesData.length,
      });

      let response;
      let usedEndpoint = "bulk-image-upload-simplified";

      try {
        response = await fetch(
          `${supabaseUrl}/functions/v1/bulk-image-upload-simplified`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            },
            body: JSON.stringify({
              projectId,
              organizationId,
              images: imagesData,
            }),
          }
        );
      } catch (networkError) {
        console.warn(
          "Simplified function not available, trying enhanced:",
          networkError
        );

        try {
          // Fallback to enhanced function if simplified doesn't exist
          usedEndpoint = "bulk-image-upload-enhanced";
          response = await fetch(
            `${supabaseUrl}/functions/v1/bulk-image-upload-enhanced`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${session.access_token}`,
              },
              body: JSON.stringify({
                projectId,
                organizationId,
                images: imagesData,
              }),
            }
          );
        } catch (enhancedError) {
          console.warn("Enhanced function also failed:", enhancedError);

          // Last fallback: direct upload (simplified version)
          usedEndpoint = "direct-upload-fallback";

          const uploadResults = [];
          const uploadErrors = [];

          for (const imageData of imagesData) {
            try {
              // Convert base64 to blob
              const byteCharacters = atob(imageData.file);
              const byteNumbers = new Array(byteCharacters.length);
              for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
              }
              const byteArray = new Uint8Array(byteNumbers);
              const blob = new Blob([byteArray], {
                type: imageData.contentType,
              });

              // Check file size against fixed limits
              const sizeInMB = blob.size / (1024 * 1024);
              if (sizeInMB > FIXED_LIMITS.max_size_mb_per_image) {
                uploadErrors.push(
                  `${imageData.name}: File too large (${sizeInMB.toFixed(
                    2
                  )}MB, max ${FIXED_LIMITS.max_size_mb_per_image}MB)`
                );
                continue;
              }

              // Generate filename using custom name
              const timestamp = Date.now();
              const extension = imageData.contentType.split("/")[1] || "jpg";
              // Use the custom name (which is already prepared in imageData.name)
              const sanitizedName = imageData.name
                .replace(/[^a-zA-Z0-9\-_.]/g, "_") // Replace invalid characters
                .replace(/\.(jpg|jpeg|png|gif|webp)$/i, ""); // Remove existing extension
              const fileName = `${projectId}/${timestamp}_${sanitizedName}.${extension}`;

              // Upload to Supabase Storage
              const { error: uploadError } = await supabase.storage
                .from("organization-images")
                .upload(fileName, blob, {
                  contentType: imageData.contentType,
                });

              if (uploadError) {
                uploadErrors.push(`${imageData.name}: ${uploadError.message}`);
                continue;
              }

              // Get public URL
              const { data: urlData } = supabase.storage
                .from("organization-images")
                .getPublicUrl(fileName);

              // Save to database
              const { error: dbError } = await supabase
                .from("organization_images")
                .insert({
                  organization_id: organizationId,
                  project_id: projectId,
                  image_name: imageData.name,
                  image_path: fileName,
                  image_url: urlData.publicUrl,
                  file_size: blob.size,
                  content_type: imageData.contentType,
                  alt_text: imageData.altText || "",
                  tags: imageData.tags || [],
                  metadata: imageData.metadata || {},
                });

              if (dbError) {
                // Clean up uploaded file
                await supabase.storage
                  .from("organization-images")
                  .remove([fileName]);
                uploadErrors.push(
                  `${imageData.name}: Database error - ${dbError.message}`
                );
                continue;
              }

              uploadResults.push({
                name: imageData.name,
                url: urlData.publicUrl,
                size: blob.size,
              });
            } catch (imgError) {
              uploadErrors.push(
                `${imageData.name}: ${
                  imgError instanceof Error ? imgError.message : "Unknown error"
                }`
              );
            }
          }

          // Handle the direct upload result
          if (uploadResults.length > 0) {
            // Clear pending images
            pendingImages.forEach((img) => URL.revokeObjectURL(img.preview));
            setPendingImages([]);
            setShowUpload(false);

            // Refresh images list
            loadImages();

            // Show success message
            let message = `Upload completed! (Fixed limits fallback)`;
            message += `\n✅ ${uploadResults.length} image(s) uploaded`;
            if (uploadErrors.length > 0) {
              message += `\n⚠️ ${uploadErrors.length} failed`;
            }
            message += `\n📈 ${images.length + uploadResults.length}/${
              FIXED_LIMITS.max_images
            } images used`;

            alert(message);
            return; // Exit early since we handled everything
          } else {
            throw new Error(`Direct upload failed: ${uploadErrors.join(", ")}`);
          }
        }
      }

      console.log("Used endpoint:", usedEndpoint);
      console.log("Response status:", response.status);
      console.log(
        "Response headers:",
        Object.fromEntries(response.headers.entries())
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Upload error response:", errorText);

        // Check if this is a subscription plan related error
        if (
          errorText.includes("limites do projeto") ||
          errorText.includes("subscription_plan") ||
          errorText.includes("get_project_image_limits")
        ) {
          console.warn(
            "Edge function has subscription plan dependency, using direct upload fallback"
          );

          // Use direct upload fallback for subscription plan errors
          const uploadResults = [];
          const uploadErrors = [];

          for (const imageData of imagesData) {
            try {
              // Convert base64 to blob
              const byteCharacters = atob(imageData.file);
              const byteNumbers = new Array(byteCharacters.length);
              for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
              }
              const byteArray = new Uint8Array(byteNumbers);
              const blob = new Blob([byteArray], {
                type: imageData.contentType,
              });

              // Check file size against fixed limits
              const sizeInMB = blob.size / (1024 * 1024);
              if (sizeInMB > FIXED_LIMITS.max_size_mb_per_image) {
                uploadErrors.push(
                  `${imageData.name}: File too large (${sizeInMB.toFixed(
                    2
                  )}MB, max ${FIXED_LIMITS.max_size_mb_per_image}MB)`
                );
                continue;
              }

              // Generate filename using custom name
              const timestamp = Date.now();
              const extension = imageData.contentType.split("/")[1] || "jpg";
              // Use the custom name (which is already prepared in imageData.name)
              const sanitizedName = imageData.name
                .replace(/[^a-zA-Z0-9\-_.]/g, "_") // Replace invalid characters
                .replace(/\.(jpg|jpeg|png|gif|webp)$/i, ""); // Remove existing extension
              const fileName = `${projectId}/${timestamp}_${sanitizedName}.${extension}`;

              // Upload to Supabase Storage
              const { error: uploadError } = await supabase.storage
                .from("organization-images")
                .upload(fileName, blob, {
                  contentType: imageData.contentType,
                });

              if (uploadError) {
                uploadErrors.push(`${imageData.name}: ${uploadError.message}`);
                continue;
              }

              // Get public URL
              const { data: urlData } = supabase.storage
                .from("organization-images")
                .getPublicUrl(fileName);

              // Save to database
              const { error: dbError } = await supabase
                .from("organization_images")
                .insert({
                  organization_id: organizationId,
                  project_id: projectId,
                  image_name: imageData.name,
                  image_path: fileName,
                  image_url: urlData.publicUrl,
                  file_size: blob.size,
                  content_type: imageData.contentType,
                  alt_text: imageData.altText || "",
                  tags: imageData.tags || [],
                  metadata: imageData.metadata || {},
                });

              if (dbError) {
                // Clean up uploaded file
                await supabase.storage
                  .from("organization-images")
                  .remove([fileName]);
                uploadErrors.push(
                  `${imageData.name}: Database error - ${dbError.message}`
                );
                continue;
              }

              uploadResults.push({
                name: imageData.name,
                url: urlData.publicUrl,
                size: blob.size,
              });
            } catch (imgError) {
              uploadErrors.push(
                `${imageData.name}: ${
                  imgError instanceof Error ? imgError.message : "Unknown error"
                }`
              );
            }
          }

          // Handle the direct upload result
          if (uploadResults.length > 0) {
            // Clear pending images
            pendingImages.forEach((img) => URL.revokeObjectURL(img.preview));
            setPendingImages([]);
            setShowUpload(false);

            // Refresh images list
            loadImages();

            // Show success message
            let message = `Upload completed! (Fixed limits fallback)`;
            message += `\n✅ ${uploadResults.length} image(s) uploaded`;
            if (uploadErrors.length > 0) {
              message += `\n⚠️ ${uploadErrors.length} failed`;
            }
            message += `\n📈 ${images.length + uploadResults.length}/${
              FIXED_LIMITS.max_images
            } images used`;

            alert(message);
            return; // Exit early since we handled everything
          } else {
            throw new Error(`Direct upload failed: ${uploadErrors.join(", ")}`);
          }
        }

        throw new Error(`Upload failed: ${response.statusText} - ${errorText}`);
      }

      const result = (await response.json()) as {
        success: boolean;
        uploadedImages?: Array<{ name: string; url: string; size: number }>;
        errors?: string[];
        summary?: {
          total_attempted: number;
          successful: number;
          failed: number;
          total_size_mb: number;
        };
        limits?: {
          current_count: number;
          max_count: number;
          current_storage_mb: number;
          max_storage_gb: number;
        };
        error?: string;
      };

      if (result.success) {
        // Clear pending images
        pendingImages.forEach((img) => URL.revokeObjectURL(img.preview));
        setPendingImages([]);
        setShowUpload(false);

        // Refresh images list
        loadImages();

        // Show detailed success message
        const summary = result.summary;
        let message = `Upload completed!`;
        if (summary) {
          message += `\n✅ ${summary.successful} image(s) uploaded`;
          if (summary.failed > 0) {
            message += `\n⚠️ ${summary.failed} failed`;
          }
        }
        // Add current count info
        message += `\n📈 ${images.length + (summary?.successful || 0)}/${
          FIXED_LIMITS.max_images
        } images used`;

        alert(message);
      } else {
        // Handle specific error cases
        if (result.error) {
          // Show limit information in error
          throw new Error(
            `${result.error}\n\nCurrent limits:\n` +
              `• Images: ${images.length}/${FIXED_LIMITS.max_images}`
          );
        } else {
          throw new Error(
            result.error || result.errors?.join(", ") || "Unknown error"
          );
        }
      }
    } catch (error) {
      console.error("Error uploading images:", error);

      let errorMessage = "Unknown error";

      if (error instanceof TypeError && error.message.includes("fetch")) {
        errorMessage = `Network error: Cannot connect to Supabase Edge Functions. 
        
Possible causes:
• Edge function 'bulk-image-upload-simplified' or 'bulk-image-upload-enhanced' doesn't exist
• CORS issues with Supabase
• Network connectivity problems
• Supabase URL incorrect: ${process.env.NEXT_PUBLIC_SUPABASE_URL}

Check browser console for more details.`;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      alert(`Upload error: ${errorMessage}`);
    } finally {
      setUploading(false);
    }
  };

  const toggleTag = (tag: string) => {
    setSelectedTags((prev) =>
      prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col m-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Select Image
          </h2>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowUpload(!showUpload)}
              disabled={uploading}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              disabled={uploading}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Upload Section */}
        {showUpload && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-slate-700">
            <div className="space-y-4">
              {/* File Input */}
              <div>
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleFileSelect}
                  className="w-full"
                  disabled={uploading}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  Select one or more images (max. 10MB each)
                </p>
              </div>

              {/* Pending Images */}
              {pendingImages.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      Images for Upload ({pendingImages.length})
                    </h4>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          pendingImages.forEach((img) =>
                            URL.revokeObjectURL(img.preview)
                          );
                          setPendingImages([]);
                        }}
                        disabled={uploading}
                      >
                        Clear All
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={handleBulkUpload}
                        disabled={uploading || pendingImages.length === 0}
                        className="flex items-center space-x-2"
                      >
                        {uploading && (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        )}
                        <span>
                          {uploading ? "Uploading..." : "Upload Images"}
                        </span>
                      </Button>
                    </div>
                  </div>

                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {pendingImages.map((pendingImage) => (
                      <div
                        key={pendingImage.id}
                        className="flex items-start space-x-3 p-3 bg-white dark:bg-slate-700 rounded-lg border border-gray-200 dark:border-gray-600"
                      >
                        <img
                          src={pendingImage.preview}
                          alt={pendingImage.name}
                          className="w-16 h-16 object-cover rounded"
                        />
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium truncate">
                              {pendingImage.name}
                            </p>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                handleRemovePendingImage(pendingImage.id)
                              }
                              disabled={uploading}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                          <div className="space-y-2">
                            <div>
                              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Custom Name
                              </label>
                              <Input
                                type="text"
                                value={pendingImage.customName}
                                onChange={(e) =>
                                  handleUpdatePendingImage(
                                    pendingImage.id,
                                    "customName",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter custom filename..."
                                className="text-sm"
                                disabled={uploading}
                              />
                            </div>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                              <div>
                                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  Alt Text
                                </label>
                                <Input
                                  type="text"
                                  value={pendingImage.altText}
                                  onChange={(e) =>
                                    handleUpdatePendingImage(
                                      pendingImage.id,
                                      "altText",
                                      e.target.value
                                    )
                                  }
                                  placeholder="Describe the image..."
                                  className="text-sm"
                                  disabled={uploading}
                                />
                              </div>
                              <div>
                                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  Tags (comma separated)
                                </label>
                                <Input
                                  type="text"
                                  value={pendingImage.tags.join(", ")}
                                  onChange={(e) =>
                                    handleUpdateTags(
                                      pendingImage.id,
                                      e.target.value
                                    )
                                  }
                                  placeholder="tag1, tag2, tag3..."
                                  className="text-sm"
                                  disabled={uploading}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search images..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedTags([])}
              disabled={selectedTags.length === 0}
            >
              Clear Filters
            </Button>
          </div>

          {/* Tags Filter */}
          {availableTags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              <div className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                <Filter className="w-4 h-4 mr-2" />
                Tags:
              </div>
              {availableTags.map((tag) => (
                <button
                  key={tag}
                  onClick={() => toggleTag(tag)}
                  className={`px-2 py-1 text-xs rounded-full border transition-colors ${
                    selectedTags.includes(tag)
                      ? "bg-blue-100 dark:bg-blue-900 border-blue-300 dark:border-blue-700 text-blue-800 dark:text-blue-200"
                      : "bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  {tag}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Fixed Limits Info */}
        <div className="px-4 py-2 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <span className="text-blue-700 dark:text-blue-300">
                📊 {images.length}/{FIXED_LIMITS.max_images} images
              </span>
            </div>
            <span className="text-xs text-blue-600 dark:text-blue-400">
              Max. {FIXED_LIMITS.max_size_mb_per_image}MB per image
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500 dark:text-gray-400">
                Loading images...
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-red-500 dark:text-red-400">
                Error: {error}
              </div>
            </div>
          ) : filteredImages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
              <ImageIcon className="w-12 h-12 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No images found</p>
              <p className="text-sm">
                {images.length === 0
                  ? "Upload some images to get started"
                  : "Try adjusting your search or filters"}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {filteredImages.map((image) => (
                <div
                  key={image.id}
                  className="group/image relative aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden transition-all"
                >
                  {/* Clickable area for image selection */}
                  <div
                    className="absolute inset-0 cursor-pointer hover:ring-2 hover:ring-blue-400 transition-all z-10"
                    onClick={() => handleImageSelect(image)}
                  >
                    <img
                      src={image.image_url}
                      alt={image.alt_text || image.image_name}
                      className="w-full h-full object-cover transition-transform group-hover/image:scale-105"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src =
                          "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f3f4f6'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='.3em' fill='%236b7280'%3ENo Image%3C/text%3E%3C/svg%3E";
                      }}
                    />

                    <div className="absolute inset-0 bg-black/0 group-hover/image:bg-black/20 transition-all" />
                    <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover/image:opacity-100 transition-opacity">
                      <p className="text-white text-xs font-medium truncate">
                        {image.alt_text || image.image_name}
                      </p>
                      {image.tags && image.tags.length > 0 && (
                        <p className="text-white/80 text-xs truncate">
                          {image.tags.join(", ")}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Delete button - Higher z-index to intercept clicks */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-1 right-1 h-6 w-6 p-0 bg-red-500 hover:bg-red-600 text-white opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 z-20"
                    onClick={(e) => handleDeleteImage(image, e)}
                    title="Delete image"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
            <span>
              {filteredImages.length} of {images.length} images
            </span>
            <span>{/* Stats will be added later if needed */}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
