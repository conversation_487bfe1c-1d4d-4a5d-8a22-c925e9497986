"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useProject } from "@/contexts";
import { RotateCcw, Loader2 } from "lucide-react";

interface RetryDeploymentButtonProps {
	className?: string;
	variant?:
		| "default"
		| "secondary"
		| "destructive"
		| "outline"
		| "ghost"
		| "link";
	size?: "default" | "sm" | "lg" | "icon";
}

interface RetryDeploymentResponse {
	success?: boolean;
	message?: string;
	error?: string;
	deployment?: {
		id: string;
		short_id: string;
		url: string;
		environment: string;
		status: string;
		created_on: string;
	};
}

export function RetryDeploymentButton({
	className,
	variant = "outline",
	size = "default",
}: RetryDeploymentButtonProps) {
	const [isLoading, setIsLoading] = useState(false);
	const { toast } = useToast();
	const { selectedProject } = useProject();

	const handleRetryDeployment = async () => {
		if (!selectedProject) {
			toast({
				title: "Erro",
				description: "Nenhum projeto selecionado",
				variant: "destructive",
			});
			return;
		}

		setIsLoading(true);

		try {
			const response = await fetch("/api/cloudflare/deployments/retry", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					projectName: selectedProject.project_name,
				}),
			});

			const data = (await response.json()) as RetryDeploymentResponse;

			if (response.ok) {
				toast({
					title: "Sucesso",
					description: "Retry do deployment iniciado com sucesso!",
				});
			} else {
				toast({
					title: "Erro",
					description: data.error || "Falha ao fazer retry do deployment",
					variant: "destructive",
				});
			}
		} catch (error) {
			console.error("Error retrying deployment:", error);
			toast({
				title: "Erro",
				description: "Erro ao conectar com o servidor",
				variant: "destructive",
			});
		} finally {
			setIsLoading(false);
		}
	};

	if (!selectedProject) {
		return null;
	}

	return (
		<Button
			onClick={handleRetryDeployment}
			disabled={isLoading}
			className={className}
			variant={variant}
			size={size}
		>
			{isLoading ? (
				<Loader2 className='h-4 w-4 animate-spin' />
			) : (
				<RotateCcw className='h-4 w-4' />
			)}
			{size !== "icon" && (
				<span className='ml-2'>
					{isLoading ? "Processando..." : "Retry Deployment"}
				</span>
			)}
		</Button>
	);
}
