import { Node } from "@tiptap/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNodeViewRenderer } from "@tiptap/react";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    tabs: {
      /**
       * Insert a tabs group with the specified attributes.
       */
      setTabs: (attributes: TabsAttrs) => ReturnType;
    };
  }
}
import React, { useState, useCallback } from "react";
import { Edit3, Save, X, Plus, Trash2, Star, GripVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import { RichTextInput } from "@/components/ui/RichTextInput";
import { useToast } from "@/components/ToastProvider";
import type { NodeViewProps } from "@tiptap/react";
// Import TiptapEditor styles for code blocks
import "../TiptapEditor.css";
import { highlightCodeBlocks } from "../utils/highlightCodeBlocks";

const slugify = (text: string): string =>
  text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, "-") // Replace spaces with -
    .replace(/[^\w-]+/g, "") // Remove all non-word chars
    .replace(/--+/g, "-"); // Replace multiple - with single -

// Interface for tab data
interface TabData {
  id: string;
  value: string;
  label: string;
  content: string;
  default?: boolean;
}

// Interface for tabs group attributes
interface TabsAttrs {
  tabs: TabData[];
}

// Individual Tab Item Component for Display
const TabDisplayItem: React.FC<{
  tab: TabData;
  isActive: boolean;
  onClick: () => void;
}> = ({ tab, isActive, onClick }) => {
  return (
    <button
      className={`px-4 py-2 text-sm font-medium border-b-2 transition-all duration-200 ${
        isActive
          ? "border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
          : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
      }`}
      onClick={onClick}
    >
      {tab.label || tab.value || "Tab"}
      {tab.default && (
        <Star className="inline-block w-3 h-3 ml-1 fill-current" />
      )}
    </button>
  );
};

// Individual Tab Item Component for Editing
const TabEditItem: React.FC<{
  tab: TabData;
  isActive: boolean;
  onClick: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onToggleDefault: () => void;
  onDragStart?: (e: React.DragEvent) => void;
  onDragOver?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent) => void;
  onDragLeave?: () => void;
  isDragging?: boolean;
  isDragOver?: boolean;
}> = ({
  tab,
  isActive,
  onClick,
  onEdit,
  onDelete,
  onToggleDefault,
  onDragStart,
  onDragOver,
  onDrop,
  onDragLeave,
  isDragging = false,
  isDragOver = false,
}) => {
  return (
    <div
      className={`relative flex-shrink-0 group ${
        isDragOver ? "drag-over-indicator" : ""
      }`}
      draggable
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDrop={onDrop}
      onDragLeave={onDragLeave}
    >
      <div
        className={`
        flex items-center gap-2 px-3 py-2 text-sm font-medium border-b-2 transition-all duration-200 cursor-pointer
        ${
          isActive
            ? "border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
            : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
        }
        ${isDragging ? "opacity-50" : ""}
      `}
      >
        <GripVertical className="w-4 h-4 text-gray-400 group-hover:text-gray-600 cursor-grab flex-shrink-0" />

        <span onClick={onClick} className="flex-grow select-none">
          {tab.label || tab.value || "Tab"}
          {tab.default && (
            <Star className="inline-block w-3 h-3 ml-1 fill-yellow-400" />
          )}
        </span>

        {/* Edit controls - visible on hover, now inside the flow */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              onToggleDefault();
            }}
            className={`h-6 w-6 text-xs ${
              tab.default
                ? "text-yellow-500 hover:text-yellow-600"
                : "text-gray-400 hover:text-gray-600"
            }`}
            title={tab.default ? "Remove default" : "Set as default"}
          >
            {tab.default ? (
              <Star className="w-3.5 h-3.5 fill-current" />
            ) : (
              <Star className="w-3.5 h-3.5" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            className="h-6 w-6 text-xs text-gray-400 hover:text-gray-600"
          >
            <Edit3 className="w-3.5 h-3.5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="h-6 w-6 text-xs text-red-400 hover:text-red-600"
          >
            <Trash2 className="w-3.5 h-3.5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

// Tab Edit Form Component
const TabEditForm: React.FC<{
  tab: TabData;
  onSave: (tab: TabData) => void;
  onCancel: () => void;
}> = ({ tab, onSave, onCancel }) => {
  const [formData, setFormData] = useState<TabData>(() => ({
    ...tab,
    value: tab.value || "",
    label: tab.label || tab.value || "",
    content: tab.content || "",
    default: tab.default || false,
  }));
  const { addToast } = useToast();

  const handleInputChange = useCallback(
    (field: keyof TabData, value: string | boolean) => {
      if (field === "label" && typeof value === "string") {
        setFormData((prev) => ({
          ...prev,
          label: value,
          value: slugify(value) || prev.id, // Auto-generate value from label
        }));
      } else {
        setFormData((prev) => ({ ...prev, [field]: value }));
      }
    },
    []
  );

  const handleSave = useCallback(() => {
    if (!formData.label.trim()) {
      addToast("Label is required", "warning");
      return;
    }
    if (!formData.content.trim()) {
      addToast("Content is required", "warning");
      return;
    }
    onSave({ ...formData, content: formData.content || "<p></p>" });
  }, [formData, onSave, addToast]);

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-slate-800 mt-4">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          Edit Tab
        </h4>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
          >
            <X className="w-3 h-3 mr-1" />
            Cancel
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleSave}
            className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save className="w-3 h-3 mr-1" />
            Save
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Label *
          </label>
          <input
            type="text"
            value={formData.label}
            onChange={(e) => handleInputChange("label", e.target.value)}
            placeholder="Tab 1"
            className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            required
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-1">
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
              Default Tab
            </label>
            <input
              type="checkbox"
              checked={formData.default || false}
              onChange={(e) => handleInputChange("default", e.target.checked)}
              className="rounded text-blue-600 focus:ring-blue-500"
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Mark this tab as the default selected tab
          </p>
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Content *
          </label>
          <RichTextInput
            value={formData.content}
            onChange={(html) => handleInputChange("content", html)}
            placeholder="Enter tab content"
            variant="default"
            className="w-full"
            enableCodeBlock={true}
            enableLink={true}
          />
        </div>
      </div>
    </div>
  );
};

// Tabs Group Component
const TabsComponent: React.FC<NodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingTabId, setEditingTabId] = useState<string | null>(null);
  const [tabs, setTabs] = useState<TabData[]>(() => {
    const nodeTabs = node.attrs.tabs;
    if (Array.isArray(nodeTabs)) {
      return nodeTabs.map((tab, index) => ({
        ...tab,
        id: tab.id || Date.now().toString() + index,
        value: tab.value || `tab-${index + 1}`,
        label: tab.label || tab.value || `Tab ${index + 1}`,
        content: tab.content || "",
        default: Boolean(tab.default),
      }));
    }
    return [];
  });
  const [activeTabId, setActiveTabId] = useState<string>(() => {
    const nodeTabs = node.attrs.tabs;
    if (Array.isArray(nodeTabs) && nodeTabs.length > 0) {
      const defaultTab = nodeTabs.find((tab) => tab.default);
      return defaultTab?.id || nodeTabs[0].id;
    }
    return "";
  });
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const { addToast } = useToast();

  const handleSave = useCallback(() => {
    updateAttributes({ tabs });
    setIsEditing(false);
    setEditingTabId(null);
    addToast("Tabs updated successfully", "success", "Success");
  }, [tabs, updateAttributes, addToast]);

  const handleCancel = useCallback(() => {
    const nodeTabs = node.attrs.tabs;
    setTabs(Array.isArray(nodeTabs) ? nodeTabs : []);
    setIsEditing(false);
    setEditingTabId(null);
  }, [node.attrs.tabs]);

  const addTab = useCallback(() => {
    const newTab: TabData = {
      id: Date.now().toString(),
      value: `tab-${tabs.length + 1}`,
      label: `Tab ${tabs.length + 1}`,
      content: "<p>Content for the new tab</p>",
      default: tabs.length === 0,
    };
    setTabs((prev) => [...prev, newTab]);
    setEditingTabId(newTab.id);
    if (tabs.length === 0) setActiveTabId(newTab.id);
    addToast("New tab added", "success", "Tab Added");
  }, [tabs.length, addToast]);

  const deleteTab = useCallback(
    (id: string) => {
      const tabToDelete = tabs.find((tab) => tab.id === id);
      const updatedTabs = tabs.filter((tab) => tab.id !== id);
      if (activeTabId === id && updatedTabs.length > 0)
        setActiveTabId(updatedTabs[0].id);
      if (tabToDelete?.default && updatedTabs.length > 0)
        updatedTabs[0].default = true;
      setTabs(updatedTabs);
      addToast("Tab deleted successfully", "info", "Tab Removed");
    },
    [tabs, activeTabId, addToast]
  );

  const editTab = useCallback((updatedTab: TabData) => {
    setTabs((prev) =>
      prev.map((tab) =>
        tab.id === updatedTab.id
          ? updatedTab
          : { ...tab, default: updatedTab.default ? false : tab.default }
      )
    );
    setEditingTabId(null);
  }, []);

  const toggleDefault = useCallback(
    (id: string) => {
      const currentDefaultId = tabs.find((t) => t.default)?.id;
      if (id === currentDefaultId) {
        addToast("This tab is already the default.", "info");
        return;
      }
      setTabs((prevTabs) =>
        prevTabs.map((tab) => ({
          ...tab,
          default: tab.id === id,
        }))
      );
    },
    [tabs, addToast]
  );

  const handleDragStart = useCallback(
    (index: number) => (e: React.DragEvent) => {
      setDraggedIndex(index);
      e.dataTransfer.effectAllowed = "move";
    },
    []
  );

  const handleDragOver = useCallback(
    (targetIndex: number) => (e: React.DragEvent) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "move";
      setDragOverIndex(targetIndex);
    },
    []
  );

  const handleDragLeave = useCallback(() => setDragOverIndex(null), []);

  const handleDrop = useCallback(
    (targetIndex: number) => (e: React.DragEvent) => {
      e.preventDefault();
      setDragOverIndex(null);
      if (draggedIndex === null || draggedIndex === targetIndex) {
        setDraggedIndex(null);
        return;
      }
      const newTabs = [...tabs];
      const [draggedTab] = newTabs.splice(draggedIndex, 1);
      newTabs.splice(targetIndex, 0, draggedTab);
      setTabs(newTabs);
      setDraggedIndex(null);
    },
    [tabs, draggedIndex]
  );

  const activeTab = tabs.find((tab) => tab.id === activeTabId) || tabs[0];

  if (isEditing) {
    return (
      <NodeViewWrapper
        className="tabs-node"
        as="div"
        data-drag-handle=""
        contentEditable={false}
        style={{ zIndex: selected ? 100 : "auto" }}
      >
        <div className="my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
              Edit Tabs
            </h4>
            <div className="flex items-center space-x-2">
              <Button
                onClick={addTab}
                size="sm"
                className="h-6 px-2 text-xs bg-green-600 hover:bg-green-700 text-white"
              >
                <Plus className="w-3 h-3 mr-1" />
                Add Tab
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
              >
                <X className="w-3 h-3 mr-1" />
                Cancel
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
                className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Save className="w-3 h-3 mr-1" />
                Save
              </Button>
            </div>
          </div>

          <div className="space-y-2 mt-4">
            {tabs.map((tab, index) =>
              editingTabId === tab.id ? (
                <TabEditForm
                  key={tab.id}
                  tab={tab}
                  onSave={editTab}
                  onCancel={() => setEditingTabId(null)}
                />
              ) : (
                <TabEditItem
                  key={tab.id}
                  tab={tab}
                  isActive={activeTabId === tab.id}
                  onClick={() => setActiveTabId(tab.id)}
                  onEdit={() => setEditingTabId(tab.id)}
                  onDelete={() => deleteTab(tab.id)}
                  onToggleDefault={() => toggleDefault(tab.id)}
                  onDragStart={handleDragStart(index)}
                  onDragOver={handleDragOver(index)}
                  onDrop={handleDrop(index)}
                  onDragLeave={handleDragLeave}
                  isDragging={draggedIndex === index}
                  isDragOver={
                    dragOverIndex === index &&
                    draggedIndex !== null &&
                    draggedIndex !== index
                  }
                />
              )
            )}
          </div>
        </div>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper className="tabs-node">
      <div
        className={`m-2 relative group bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 ${
          selected ? "ring-2 ring-blue-400/50" : ""
        }`}
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            setIsEditing(true);
          }}
          className="absolute top-1 right-1 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700 z-10"
        >
          <Edit3 className="w-3 h-3 mr-1" />
          Edit
        </Button>

        <div className="flex border-b border-gray-200 dark:border-gray-600 overflow-x-auto rounded-t-lg">
          {tabs.map((tab) => (
            <TabDisplayItem
              key={tab.id}
              tab={tab}
              isActive={activeTabId === tab.id}
              onClick={() => setActiveTabId(tab.id)}
            />
          ))}
        </div>

        <div className="p-4 min-h-[100px] rounded-b-lg">
          {activeTab ? (
            <div
              className="prose dark:prose-invert max-w-none tabs-content-wrapper tabs-content active"
              dangerouslySetInnerHTML={{
                __html: highlightCodeBlocks(activeTab.content || ""),
              }}
            />
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No tabs configured. Edit to add tabs.
            </div>
          )}
        </div>
      </div>
    </NodeViewWrapper>
  );
};

// Tabs Node Extension
export const TabsNode = Node.create({
  name: "tabs",
  group: "block",
  content: "",
  atom: true,
  draggable: false,

  addAttributes() {
    return {
      tabs: {
        default: [],
        parseHTML: (element) => {
          try {
            const tabsData =
              element.getAttribute("data-tabs") || element.getAttribute("tabs");
            if (tabsData) return JSON.parse(tabsData);

            const tabElements = element.querySelectorAll(
              "Tab, tab, div[data-type='tab']"
            );
            return Array.from(tabElements).map((tabEl, index) => {
              const value = tabEl.getAttribute("value") || `tab-${index + 1}`;
              const label =
                tabEl.getAttribute("label") || value || `Tab ${index + 1}`;
              return {
                id: Date.now().toString() + index,
                value,
                label,
                content:
                  tabEl.getAttribute("content") ||
                  tabEl.textContent ||
                  tabEl.innerHTML ||
                  "",
                default: tabEl.hasAttribute("default") || index === 0,
              };
            });
          } catch {
            return [];
          }
        },
        renderHTML: (attributes) => ({
          "data-tabs": JSON.stringify(attributes.tabs || []),
        }),
      },
    };
  },

  parseHTML() {
    return [{ tag: "Tabs" }, { tag: "div[data-type='tabs']" }];
  },

  renderHTML({ HTMLAttributes }) {
    const tabs = HTMLAttributes.tabs || HTMLAttributes["data-tabs"] || [];
    let tabsList = [];
    if (typeof tabs === "string") {
      try {
        tabsList = JSON.parse(tabs);
      } catch {
        tabsList = [];
      }
    } else if (Array.isArray(tabs)) {
      tabsList = tabs;
    }

    const tabElements = tabsList.map((tab: TabData) => [
      "div",
      {
        "data-type": "tab",
        value: tab.value,
        label: tab.label,
        content: tab.content || "",
        ...(tab.default && { default: "true" }),
      },
      "",
    ]);

    return [
      "div",
      {
        "data-type": "tabs",
        "data-tabs": JSON.stringify(tabsList),
        class: "tabs-node",
      },
      ...tabElements,
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(TabsComponent);
  },

  addCommands() {
    return {
      setTabs:
        (attributes: TabsAttrs) =>
        ({ commands }) =>
          commands.insertContent({
            type: this.name,
            attrs: attributes,
          }),
    };
  },
});

export default TabsNode;
