import { Node } from "@tiptap/core";
import { <PERSON>de<PERSON>iew<PERSON>rapper, ReactNodeViewRenderer } from "@tiptap/react";
import React, { useState, useCallback, useEffect, useRef } from "react";
import { Edit3, Save, X, ChevronDown } from "lucide-react";
import { RiComputerLine } from "react-icons/ri";
import { TbInfoSquareRounded } from "react-icons/tb";
import { MdOutlineVerified } from "react-icons/md";
import { PiWarningCircleBold } from "react-icons/pi";
import { FiTool } from "react-icons/fi";
import { LuTriangleAlert } from "react-icons/lu";
import { Button } from "@/components/ui/button";
import { RichTextInput } from "@/components/ui/RichTextInput";
import { useToast } from "@/components/ToastProvider";
import type { NodeViewProps } from "@tiptap/react";

// Interface for callout attributes
interface CalloutAttrs {
  type: "info" | "success" | "warning" | "danger" | "support" | "training";
  title?: string;
  description: string;
}

// Map type to icon and styles
const calloutConfig = {
  info: {
    icon: TbInfoSquareRounded,
    bgColor: "bg-[#e8efff]",
    borderColor: "border-[#3778fd]",
    iconColor: "text-[#3778fd]",
    titleColor: "text-[#3778fd]",
  },
  success: {
    icon: MdOutlineVerified,
    bgColor: "bg-[#eafff4]",
    borderColor: "border-[#208f59]",
    iconColor: "text-[#208f59]",
    titleColor: "text-[#208f59]",
  },
  warning: {
    icon: PiWarningCircleBold,
    bgColor: "bg-[#fff9f1]",
    borderColor: "border-[#f4982b] ",
    iconColor: "text-[#f5ab53] ",
    titleColor: "text-[#f5ab53] ",
  },
  danger: {
    icon: LuTriangleAlert,
    bgColor: "bg-[#fff1f1]",
    borderColor: "border-[#ff4d51] ",
    iconColor: "text-[#ff6164] ",
    titleColor: "text-[#ff6164] ",
  },
  support: {
    icon: FiTool,
    bgColor: "bg-[#fff5dd]",
    borderColor: "border-[#ff9900] ",
    iconColor: "text-[#ff9900] ",
    titleColor: "text-[#ff9900] ",
  },
  training: {
    icon: RiComputerLine,
    bgColor: "bg-[#f7f7f7]",
    borderColor: "border-[#333333] ",
    iconColor: "text-[#333333] ",
    titleColor: "text-[#333333] ",
  },
};

// Callout Component
const CalloutComponent: React.FC<NodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);
  const { addToast } = useToast();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState<CalloutAttrs>({
    type: node.attrs.type || "info",
    title: node.attrs.title || "",
    description: node.attrs.description || "",
  });

  const config = calloutConfig[formData.type] || calloutConfig.info;
  const Icon = config.icon;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as globalThis.Node)
      ) {
        setIsTypeDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSave = useCallback(() => {
    // Validate required fields
    if (!formData.type) {
      addToast("Type is required", "warning");
      return;
    }

    // Check if description has content (including HTML)
    const descriptionText = formData.description
      .replace(/<[^>]*>/g, "") // Remove HTML tags
      .trim();

    if (!descriptionText) {
      addToast("Description is required", "warning");
      return;
    }

    // Create a clean copy of formData
    const cleanFormData = {
      ...formData,
      // Ensure description is properly formatted
      description: formData.description || "<p></p>",
    };

    updateAttributes(cleanFormData);
    setIsEditing(false);
    addToast("Callout updated successfully", "success", "Success");
  }, [formData, updateAttributes, addToast]);

  const handleCancel = useCallback(() => {
    setFormData({
      type: node.attrs.type || "info",
      title: node.attrs.title || "",
      description: node.attrs.description || "",
    });
    setIsEditing(false);
  }, [node.attrs]);

  const handleInputChange = useCallback(
    (field: keyof CalloutAttrs, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  if (isEditing) {
    return (
      <NodeViewWrapper
        className="callout-node"
        as="div"
        data-drag-handle=""
        contentEditable={false}
      >
        <div className="my-4 p-4 pb-8 pt-2 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg transition-all duration-300 ease-in-out">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
              Edit Callout
            </h4>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
              >
                <X className="w-3 h-3 mr-1" />
                Cancel
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
                className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Save className="w-3 h-3 mr-1" />
                Save
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Type *
              </label>
              <div className="relative " ref={dropdownRef}>
                <button
                  type="button"
                  onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                  className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white flex items-center justify-between "
                >
                  <div className="flex items-center gap-2">
                    {(() => {
                      const selectedConfig = calloutConfig[formData.type];
                      const SelectedIcon = selectedConfig.icon;
                      return (
                        <>
                          <div
                            className={`p-1 rounded ${selectedConfig.bgColor}`}
                          >
                            <SelectedIcon
                              className={`w-4 h-4 ${selectedConfig.iconColor}`}
                            />
                          </div>
                          <span className="capitalize">{formData.type}</span>
                        </>
                      );
                    })()}
                  </div>
                  <ChevronDown
                    className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                      isTypeDropdownOpen ? "rotate-180" : ""
                    }`}
                  />
                </button>

                {isTypeDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg overflow-hidden animate-in fade-in slide-in-from-top-1 duration-200">
                    {(Object.keys(calloutConfig) as CalloutAttrs["type"][]).map(
                      (type, index) => {
                        const config = calloutConfig[type];
                        const Icon = config.icon;
                        return (
                          <button
                            key={type}
                            type="button"
                            onClick={() => {
                              handleInputChange("type", type);
                              setIsTypeDropdownOpen(false);
                            }}
                            className={`w-full px-3 py-2.5 text-sm text-left hover:bg-gray-50 dark:hover:bg-slate-600 flex items-center gap-3 transition-all duration-150 ${
                              formData.type === type
                                ? "bg-gray-50 dark:bg-slate-600"
                                : ""
                            } ${
                              index === 0
                                ? ""
                                : "border-t border-gray-100 dark:border-gray-600"
                            }`}
                          >
                            <div
                              className={`p-1.5 rounded-md ${config.bgColor} ${config.borderColor} border transition-transform duration-150 hover:scale-110`}
                            >
                              <Icon className={`w-4 h-4 ${config.iconColor}`} />
                            </div>
                            <div className="flex-1">
                              <span
                                className={`capitalize font-medium ${config.titleColor}`}
                              >
                                {type}
                              </span>
                              {/* Adicionar uma pequena descrição para cada tipo */}
                              <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                {type === "info" && "General information"}
                                {type === "success" &&
                                  "Positive outcome or confirmation"}
                                {type === "warning" &&
                                  "Important notice or caution"}
                                {type === "danger" && "Critical alert or error"}
                                {type === "support" &&
                                  "Help or assistance info"}
                                {type === "training" && "Educational content"}
                              </p>
                            </div>
                          </button>
                        );
                      }
                    )}
                  </div>
                )}
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title (Optional)
              </label>
              <input
                type="text"
                value={formData.title || ""}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="Enter callout title"
                className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description *
              </label>
              <RichTextInput
                value={formData.description}
                onChange={(html) => handleInputChange("description", html)}
                placeholder="Enter callout description"
                variant="compact"
                className="w-full"
                enableCodeBlock={true}
                enableLink={true}
              />
            </div>
          </div>
        </div>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper className="callout-node">
      <div
        className={`px-4 rounded-md ${
          formData.title ? "min-h-[90px]" : "min-h-[50px]"
        } border-l-4 ${config.bgColor} ${
          config.borderColor
        } relative group transition-all duration-200 ${
          selected ? "ring-2 ring-blue-400/50" : ""
        }`}
      >
        {/* Edit button - only visible on hover */}
        {selected && (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
            className="absolute top-2 right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700"
          >
            <Edit3 className="w-3 h-3 mr-1" />
            Edit
          </Button>
        )}

        <div className={`flex  ${formData.title ? "flex-col pt-0" : "pt-3"}`}>
          {/* Header com ícone e título alinhados */}
          <div className="flex items-center gap-3 h-fit -mb-2.5">
            <Icon className={`w-6 h-6 ${config.iconColor}`} />
            {formData.title && (
              <h3 className={`font-semibold text-base ${config.titleColor}`}>
                {formData.title}
              </h3>
            )}
          </div>

          {/* Descrição abaixo do título */}
          <p className={`text-sm !mb-0 ${!formData.title ? "ml-4 " : "ml-9"}`}>
            {node.attrs.description ? (
              <span
                dangerouslySetInnerHTML={{ __html: node.attrs.description }}
              />
            ) : (
              "Callout description"
            )}
          </p>
        </div>
      </div>
    </NodeViewWrapper>
  );
};

// Callout Node Extension
export const CalloutNode = Node.create({
  name: "callout",
  group: "block",
  content: "",
  atom: true,
  draggable: false,

  addAttributes() {
    return {
      type: {
        default: "info",
        parseHTML: (element) =>
          element.getAttribute("type") ||
          element.getAttribute("data-type") ||
          "info",
        renderHTML: (attributes) => {
          if (!attributes.type) return {};
          return { type: attributes.type };
        },
      },
      title: {
        default: "",
        parseHTML: (element) =>
          element.getAttribute("title") || element.getAttribute("data-title"),
        renderHTML: (attributes) => {
          if (!attributes.title) return {};
          return { title: attributes.title };
        },
      },
      description: {
        default: "",
        parseHTML: (element) =>
          element.getAttribute("description") ||
          element.getAttribute("data-description"),
        renderHTML: (attributes) => {
          if (!attributes.description) return {};
          return { description: attributes.description };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="callout"]',
      },
      {
        tag: "callout",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      {
        ...HTMLAttributes,
        "data-type": "callout",
        class: "callout-node",
      },
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(CalloutComponent);
  },

  addCommands() {
    return {
      setCallout:
        (attributes?: CalloutAttrs) =>
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ({ commands }: { commands: any }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes,
          });
        },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;
  },
});

export default CalloutNode;
