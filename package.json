{"name": "new-dashboard-cloudflare", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "pages:build": "npx @cloudflare/next-on-pages", "preview": "npm run pages:build && wrangler pages dev", "deploy": "npm run pages:build && wrangler pages deploy", "cf-typegen": "wrangler types --env-interface CloudflareEnv env.d.ts"}, "dependencies": {"@harshtalks/slash-tiptap": "^1.3.0", "@hookform/resolvers": "^5.0.1", "@octokit/webhooks-types": "^7.6.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/ssr": "^0.6.1", "@types/exceljs": "^0.5.3", "@supabase/supabase-js": "^2.49.4", "@tiptap/extension-bubble-menu": "^2.14.0", "@tiptap/extension-code-block-lowlight": "^2.12.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/marked": "^5.0.2", "@types/turndown": "^5.0.5", "exceljs": "^4.4.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "framer-motion": "^12.10.0", "gray-matter": "^4.0.3", "hast-util-to-html": "^9.0.5", "highlight.js": "^11.11.1", "js-yaml": "^4.1.0", "lowlight": "^3.3.0", "lucide-react": "^0.507.0", "marked": "^15.0.12", "next": "15.3.1", "octokit": "^4.1.3", "phosphor-react": "^1.4.1", "react-payment-logos": "^1.1.0", "stripe": "^18.2.0", "postcss": "^8.5.3", "resend": "^4.2.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-icons": "^5.5.0", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "rehype-stringify": "^10.0.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-mdx": "^3.1.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "remark-stringify": "^11.0.0", "tailwind-merge": "^3.2.0", "turndown": "^7.2.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "zod": "^3.24.4"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@cloudflare/workers-types": "^4.20250505.0", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.5", "@types/hast": "^3.0.4", "@types/js-yaml": "^4.0.9", "@types/mdast": "^4.0.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.9", "typescript": "^5", "vercel": "^41.7.0", "wrangler": "^4.14.1"}}